// Frida script for auto-collecting ruins in Unity IL2CPP game
// Based on GoodyHutHelper class analysis

Java.perform(function() {
    console.log("[+] Starting Ruins Auto-Collector");
    
    // First, let's find the correct IL2CPP module name
    console.log("[*] Enumerating modules to find IL2CPP...");
    var il2cpp = null;
    var possibleNames = ["libil2cpp.so", "libunity.so", "libmain.so", "libgame.so"];
    
    // List all loaded modules
    Process.enumerateModules().forEach(function(module) {
        console.log("[*] Found module: " + module.name + " at " + module.base);
        
        // Check for common IL2CPP module names
        if (module.name.toLowerCase().includes("il2cpp") || 
            module.name.toLowerCase().includes("unity") ||
            module.name.toLowerCase().includes("libmain") ||
            module.name.toLowerCase().includes("libgame")) {
            il2cpp = module;
            console.log("[+] Potential IL2CPP module found: " + module.name);
        }
    });
    
    if (!il2cpp) {
        console.log("[-] Could not find IL2CPP module. Please check module list above.");
        return;
    }
    
    console.log("[+] Using module: " + il2cpp.name + " at base: " + il2cpp.base);
    
    // Calculate actual addresses using module base + RVA offsets
    var canCollectAddr = il2cpp.base.add(0x209D258);  // CanCollect() RVA
    var finishCollectAddr = il2cpp.base.add(0x209B924);  // FinishCollect() RVA
    
    console.log("[*] CanCollect address: " + canCollectAddr);
    console.log("[*] FinishCollect address: " + finishCollectAddr);
    var stateMachineOffset = 0x18;  // m_stateMachine field offset
    
    // State constants
    var STATE_CLEANING_UP = "cleaning_up";
    
    // Hook CanCollect to auto-trigger collection
    Interceptor.attach(canCollectAddr, {
        onEnter: function(args) {
            this.thisPtr = this.context.x0;  // 'this' pointer (GoodyHutHelper instance)
            console.log("[*] CanCollect() called on GoodyHutHelper: " + this.thisPtr);
        },
        
        onLeave: function(retval) {
            var canCollect = retval.toInt32();
            console.log("[*] CanCollect() returned: " + canCollect);
            
            if (canCollect === 1) {  // true in IL2CPP
                console.log("[+] Ruins can be collected! Auto-triggering...");
                
                try {
                    // Call FinishCollect() on the same instance
                    var finishCollectFunc = new NativeFunction(finishCollectAddr, 'void', ['pointer']);
                    finishCollectFunc(this.thisPtr);
                    console.log("[+] FinishCollect() executed successfully");
                    
                    // Update state to STATE_CLEANING_UP
                    updateEntityState(this.thisPtr, STATE_CLEANING_UP);
                    
                } catch (e) {
                    console.log("[-] Error executing FinishCollect(): " + e);
                }
            }
        }
    });
    
    // Helper function to update entity state
    function updateEntityState(goodyHutPtr, newState) {
        try {
            // Get StateMachine pointer (offset 0x18)
            var stateMachinePtr = goodyHutPtr.add(stateMachineOffset).readPointer();
            
            if (!stateMachinePtr.isNull()) {
                console.log("[*] Updating state to: " + newState);
                
                // Find il2cpp_string_new to create state string
                var stringNewFunc = Module.findExportByName("libil2cpp.so", "il2cpp_string_new");
                if (stringNewFunc) {
                    var createString = new NativeFunction(stringNewFunc, 'pointer', ['pointer']);
                    var stateString = createString(Memory.allocUtf8String(newState));
                    
                    // You would need to find the StateMachine's SetState method
                    // This is a simplified example - actual implementation depends on StateMachine structure
                    console.log("[+] State string created: " + stateString);
                }
            }
        } catch (e) {
            console.log("[-] Error updating state: " + e);
        }
    }
    
    // Hook Update() to continuously check for collectible ruins
    var updateAddr = il2cpp.base.add(0x209CF60);  // Update() RVA
    var updateCallCount = 0;
    
    Interceptor.attach(updateAddr, {
        onEnter: function(args) {
            updateCallCount++;
            
            // Check every 60 update calls (roughly 1 second at 60 FPS)
            if (updateCallCount % 60 === 0) {
                var thisPtr = this.context.x0;
                
                // Call CanCollect() to check if ready
                var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                var result = canCollectFunc(thisPtr);
                
                if (result === 1) {
                    console.log("[*] Update() detected collectible ruins at: " + thisPtr);
                }
            }
        }
    });
    
    // Hook GetRewardType and GetRewardAmount for logging
    Interceptor.attach(il2cpp.base.add(0x209CC3C), {  // GetRewardType RVA
        onLeave: function(retval) {
            var rewardType = retval.toInt32();
            console.log("[*] Reward Type: " + rewardType);
        }
    });
    
    Interceptor.attach(il2cpp.base.add(0x209D9C4), {  // GetRewardAmount RVA
        onLeave: function(retval) {
            var amount = retval.toInt32();
            console.log("[*] Reward Amount: " + amount);
        }
    });
    
    console.log("[+] Ruins Auto-Collector hooks installed");
    console.log("[+] Will auto-collect any ruins when CanCollect() returns true");
});

// Helper function to manually trigger collection on specific entity
function manualCollect(goodyHutAddress) {
    var goodyHutPtr = ptr(goodyHutAddress);
    
    // Find IL2CPP module first
    var il2cpp = null;
    Process.enumerateModules().forEach(function(module) {
        if (module.name.toLowerCase().includes("il2cpp") || 
            module.name.toLowerCase().includes("unity") ||
            module.name.toLowerCase().includes("libmain")) {
            il2cpp = module;
        }
    });
    
    if (!il2cpp) {
        console.log("[-] IL2CPP module not found for manual collection");
        return false;
    }
    
    var canCollectFunc = new NativeFunction(il2cpp.base.add(0x209D258), 'int', ['pointer']);
    var finishCollectFunc = new NativeFunction(il2cpp.base.add(0x209B924), 'void', ['pointer']);
    
    if (canCollectFunc(goodyHutPtr) === 1) {
        finishCollectFunc(goodyHutPtr);
        console.log("[+] Manual collection completed for: " + goodyHutAddress);
        return true;
    } else {
        console.log("[-] Cannot collect ruins at: " + goodyHutAddress);
        return false;
    }
}

// Usage: manualCollect("0x12345678")  // Replace with actual GoodyHutHelper instance address