// Frida script for auto-collecting ruins in Unity IL2CPP game
// Based on GoodyHutHelper class analysis

Java.perform(function() {
    console.log("[+] Starting Ruins Auto-Collector");
    
    // Enhanced IL2CPP module detection for Dominations
    console.log("[*] Enumerating modules to find IL2CPP/Unity engine...");
    var il2cpp = null;
    var potentialModules = [];

    // System libraries to exclude (common Android system libs)
    var systemLibs = [
        "libc.so", "libm.so", "libdl.so", "liblog.so", "libz.so", "libstdc++.so",
        "libandroid.so", "libEGL.so", "libGLESv2.so", "libOpenSLES.so",
        "libvulkan.so", "libmediandk.so", "libaaudio.so", "libamidi.so",
        "libbinder.so", "libutils.so", "libcutils.so", "libhardware.so",
        "libui.so", "libgui.so", "libinput.so", "libsensor.so",
        "libcrypto.so", "libssl.so", "libicuuc.so", "libicui18n.so",
        "libsqlite.so", "libexpat.so", "libpng.so", "libjpeg.so",
        "libwebp.so", "libfreetype.so", "libharfbuzz.so", "libskia.so",
        "libft2.so", "libfontconfig.so", "libxml2.so", "libxslt.so"
    ];

    // List all loaded modules with detailed analysis
    Process.enumerateModules().forEach(function(module) {
        var moduleName = module.name.toLowerCase();
        var moduleSize = module.size;
        var moduleBase = module.base;

        console.log("[*] Found module: " + module.name + " at " + module.base + " (size: " + moduleSize + ")");

        // Skip system libraries
        var isSystemLib = systemLibs.some(function(sysLib) {
            return moduleName === sysLib.toLowerCase();
        });

        if (isSystemLib) {
            return; // Skip system libraries
        }

        // Check for obvious IL2CPP/Unity patterns
        if (moduleName.includes("il2cpp") ||
            moduleName.includes("unity") ||
            moduleName.includes("libmain") ||
            moduleName.includes("libgame") ||
            moduleName.includes("libcocos") ||
            moduleName.includes("libue4") ||
            moduleName.includes("libengine")) {
            il2cpp = module;
            console.log("[+] FOUND IL2CPP module (pattern match): " + module.name);
            return;
        }

        // Check for Dominations-specific patterns
        if (moduleName.includes("domination") ||
            moduleName.includes("nexon") ||
            moduleName.includes("adk") ||
            moduleName.includes("game") ||
            moduleName.includes("client")) {
            potentialModules.push({module: module, reason: "game-specific name", priority: 1});
            console.log("[+] Potential IL2CPP module (game-specific): " + module.name);
        }

        // Check for suspicious/obfuscated names (short, cryptic names)
        else if (moduleName.match(/^lib[a-z0-9]{1,8}\.so$/) && moduleSize > 5000000) { // > 5MB
            potentialModules.push({module: module, reason: "suspicious name + large size", priority: 2});
            console.log("[+] Potential IL2CPP module (suspicious): " + module.name);
        }

        // Check for large modules (likely game engine)
        else if (moduleSize > 10000000) { // > 10MB
            potentialModules.push({module: module, reason: "large size", priority: 3});
            console.log("[+] Potential IL2CPP module (large): " + module.name);
        }

        // Check for modules loaded at higher addresses (app-specific)
        else if (parseInt(moduleBase.toString(), 16) > 0x70000000 && moduleSize > 2000000) { // > 2MB
            potentialModules.push({module: module, reason: "high address + decent size", priority: 4});
            console.log("[+] Potential IL2CPP module (high address): " + module.name);
        }
    });

    // If no obvious IL2CPP module found, select best candidate
    if (!il2cpp && potentialModules.length > 0) {
        // Sort by priority (lower number = higher priority)
        potentialModules.sort(function(a, b) { return a.priority - b.priority; });

        il2cpp = potentialModules[0].module;
        console.log("[+] Selected best candidate IL2CPP module: " + il2cpp.name + " (reason: " + potentialModules[0].reason + ")");

        // Show all candidates for reference
        console.log("[*] All potential candidates:");
        potentialModules.forEach(function(candidate, index) {
            console.log("  " + (index + 1) + ". " + candidate.module.name + " - " + candidate.reason);
        });
    }
    
    if (!il2cpp) {
        console.log("[-] Could not find IL2CPP module. Please check module list above.");
        return;
    }
    
    console.log("[+] Using module: " + il2cpp.name + " at base: " + il2cpp.base);
    
    // Calculate actual addresses using module base + RVA offsets
    var canCollectAddr = il2cpp.base.add(0x209D258);  // CanCollect() RVA
    var finishCollectAddr = il2cpp.base.add(0x209B924);  // FinishCollect() RVA
    
    console.log("[*] CanCollect address: " + canCollectAddr);
    console.log("[*] FinishCollect address: " + finishCollectAddr);
    var stateMachineOffset = 0x18;  // m_stateMachine field offset
    
    // State constants
    var STATE_CLEANING_UP = "cleaning_up";
    
    // Hook CanCollect to auto-trigger collection
    Interceptor.attach(canCollectAddr, {
        onEnter: function(args) {
            this.thisPtr = this.context.x0;  // 'this' pointer (GoodyHutHelper instance)
            console.log("[*] CanCollect() called on GoodyHutHelper: " + this.thisPtr);
        },
        
        onLeave: function(retval) {
            var canCollect = retval.toInt32();
            console.log("[*] CanCollect() returned: " + canCollect);
            
            if (canCollect === 1) {  // true in IL2CPP
                console.log("[+] Ruins can be collected! Auto-triggering...");
                
                try {
                    // Call FinishCollect() on the same instance
                    var finishCollectFunc = new NativeFunction(finishCollectAddr, 'void', ['pointer']);
                    finishCollectFunc(this.thisPtr);
                    console.log("[+] FinishCollect() executed successfully");
                    
                    // Update state to STATE_CLEANING_UP
                    updateEntityState(this.thisPtr, STATE_CLEANING_UP);
                    
                } catch (e) {
                    console.log("[-] Error executing FinishCollect(): " + e);
                }
            }
        }
    });
    
    // Helper function to update entity state and trigger cleanup
    function updateEntityState(goodyHutPtr, newState) {
        try {
            // Get StateMachine pointer (offset 0x18)
            var stateMachinePtr = goodyHutPtr.add(stateMachineOffset).readPointer();

            if (!stateMachinePtr.isNull()) {
                console.log("[*] Updating state to: " + newState);

                // Find il2cpp_string_new to create state string
                var stringNewFunc = Module.findExportByName("libil2cpp.so", "il2cpp_string_new");
                if (stringNewFunc) {
                    var createString = new NativeFunction(stringNewFunc, 'pointer', ['pointer']);
                    var stateString = createString(Memory.allocUtf8String(newState));

                    // You would need to find the StateMachine's SetState method
                    // This is a simplified example - actual implementation depends on StateMachine structure
                    console.log("[+] State string created: " + stateString);
                }

                // If state is cleaning up, trigger automatic selling after a short delay
                if (newState === STATE_CLEANING_UP) {
                    setTimeout(function() {
                        try {
                            console.log("[*] Attempting automatic ruin selling...");
                            var sellRuinsAddr = il2cpp.base.add(0x209DE3C);  // SellRuins() RVA
                            var sellRuinsFunc = new NativeFunction(sellRuinsAddr, 'void', ['pointer']);
                            sellRuinsFunc(goodyHutPtr);
                            console.log("[+] SellRuins() executed for automatic cleanup");
                        } catch (e) {
                            console.log("[-] Error in automatic ruin selling: " + e);
                        }
                    }, 1000);  // 1 second delay
                }
            }
        } catch (e) {
            console.log("[-] Error updating state: " + e);
        }
    }
    
    // Batch Processing System for Ruins Collection
    var BATCH_SIZE = 15;  // Process 15 instances per batch
    var BATCH_INTERVAL = 3000;  // 3 seconds between batches
    var RETRY_LIMIT = 3;  // Maximum retry attempts per instance

    var discoveredInstances = new Map();  // instanceId -> {ptr, discoveryTime, retryCount}
    var processedInstances = new Set();   // Successfully processed instances
    var failedInstances = new Map();      // instanceId -> {ptr, lastFailTime, retryCount}
    var currentBatch = [];
    var batchNumber = 0;
    var isProcessingBatch = false;

    // Discovery phase - collect all GoodyHutHelper instances
    var updateAddr = il2cpp.base.add(0x209CF60);  // Update() RVA
    var updateCallCount = 0;

    Interceptor.attach(updateAddr, {
        onEnter: function(args) {
            updateCallCount++;

            // Discovery phase - check every 30 update calls (roughly 0.5 seconds at 60 FPS)
            if (updateCallCount % 30 === 0) {
                var thisPtr = this.context.x0;
                var instanceId = thisPtr.toString();

                // Call CanCollect() to check if ready
                var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                var result = canCollectFunc(thisPtr);

                if (result === 1) {
                    // Add to discovered instances if not already processed
                    if (!processedInstances.has(instanceId) && !discoveredInstances.has(instanceId)) {
                        discoveredInstances.set(instanceId, {
                            ptr: thisPtr,
                            discoveryTime: Date.now(),
                            retryCount: 0
                        });
                        console.log("[*] Discovered collectible ruins at: " + thisPtr + " (Total discovered: " + discoveredInstances.size + ")");
                    }
                }
            }
        }
    });

    // Batch processing function
    function processCollectionBatch() {
        if (isProcessingBatch) {
            console.log("[*] Batch processing already in progress, skipping...");
            return;
        }

        isProcessingBatch = true;
        batchNumber++;

        // Prepare current batch from discovered instances and failed retries
        currentBatch = [];
        var batchInstances = [];

        // Add discovered instances to batch
        var discoveredArray = Array.from(discoveredInstances.entries());
        for (var i = 0; i < Math.min(BATCH_SIZE, discoveredArray.length); i++) {
            var [instanceId, data] = discoveredArray[i];
            batchInstances.push({id: instanceId, data: data, source: 'discovered'});
            discoveredInstances.delete(instanceId);
        }

        // Add failed instances ready for retry
        var now = Date.now();
        var failedArray = Array.from(failedInstances.entries());
        var remainingBatchSlots = BATCH_SIZE - batchInstances.length;

        for (var i = 0; i < Math.min(remainingBatchSlots, failedArray.length); i++) {
            var [instanceId, failData] = failedArray[i];
            // Retry after 10 seconds and if retry count is under limit
            if (now - failData.lastFailTime > 10000 && failData.retryCount < RETRY_LIMIT) {
                batchInstances.push({id: instanceId, data: failData, source: 'retry'});
                failedInstances.delete(instanceId);
            }
        }

        if (batchInstances.length === 0) {
            console.log("[*] Batch #" + batchNumber + ": No instances to process");
            isProcessingBatch = false;
            return;
        }

        console.log("[+] Starting Batch #" + batchNumber + " - Processing " + batchInstances.length + " instances");
        console.log("[*] Batch composition: " + batchInstances.filter(b => b.source === 'discovered').length + " discovered, " +
                   batchInstances.filter(b => b.source === 'retry').length + " retries");

        var batchSuccesses = 0;
        var batchFailures = 0;

        // Process each instance in the batch
        batchInstances.forEach(function(batchItem, index) {
            setTimeout(function() {
                try {
                    var instanceId = batchItem.id;
                    var instanceData = batchItem.data;
                    var thisPtr = instanceData.ptr;

                    console.log("[*] Batch #" + batchNumber + " [" + (index + 1) + "/" + batchInstances.length + "] Processing: " + instanceId);

                    // Validate pointer before any operations
                    if (thisPtr.isNull() || thisPtr.equals(ptr(0))) {
                        console.log("[-] Invalid thisPtr for instance " + instanceId + ", skipping");
                        batchFailures++;
                        return;
                    }

                    // Verify instance is still collectible with error handling
                    var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                    var canCollect;

                    try {
                        canCollect = canCollectFunc(thisPtr);
                    } catch (e) {
                        console.log("[-] Error calling CanCollect() for " + instanceId + ": " + e);
                        batchFailures++;
                        return;
                    }

                    if (canCollect !== 1) {
                        console.log("[*] Instance " + instanceId + " no longer collectible, skipping");
                        batchSuccesses++; // Count as success since it's no longer needed
                        return;
                    }

                    // Safely get config and set cleanUp flag
                    var configAddr = il2cpp.base.add(0x209B54C);  // Config() RVA
                    var configFunc = new NativeFunction(configAddr, 'pointer', ['pointer']);
                    var configPtr = null;

                    try {
                        configPtr = configFunc(thisPtr);
                        console.log("[*] Config() returned: " + configPtr + " for instance: " + instanceId);
                    } catch (e) {
                        console.log("[-] Error calling Config() for " + instanceId + ": " + e);
                        // Continue without setting cleanUp flag
                    }

                    if (configPtr && !configPtr.isNull() && !configPtr.equals(ptr(0))) {
                        try {
                            // Validate memory before writing
                            var cleanUpOffset = 0x30;
                            var cleanUpAddr = configPtr.add(cleanUpOffset);

                            // Try to read first to validate memory access
                            var currentValue = cleanUpAddr.readU8();
                            console.log("[*] Current cleanUp value: " + currentValue + " at " + cleanUpAddr);

                            // Set cleanUp flag to true
                            cleanUpAddr.writeU8(1);
                            console.log("[+] Set cleanUp flag for: " + instanceId);
                        } catch (e) {
                            console.log("[-] Error setting cleanUp flag for " + instanceId + ": " + e);
                            // Continue with collection anyway
                        }
                    } else {
                        console.log("[-] Warning: Config() returned null/invalid pointer for " + instanceId);
                    }

                    // Execute collection with error handling
                    var finishCollectFunc = new NativeFunction(finishCollectAddr, 'void', ['pointer']);
                    try {
                        finishCollectFunc(thisPtr);
                        console.log("[+] FinishCollect() executed for: " + instanceId);
                    } catch (e) {
                        console.log("[-] Error calling FinishCollect() for " + instanceId + ": " + e);
                        batchFailures++;
                        return;
                    }

                    // Mark as successfully processed
                    processedInstances.add(instanceId);
                    batchSuccesses++;
                    console.log("[+] Batch #" + batchNumber + " [" + (index + 1) + "/" + batchInstances.length + "] SUCCESS: " + instanceId);

                } catch (e) {
                    console.log("[-] Batch #" + batchNumber + " [" + (index + 1) + "/" + batchInstances.length + "] FAILED: " + batchItem.id + " - " + e);

                    // Add to failed instances for retry
                    var retryCount = (batchItem.data.retryCount || 0) + 1;
                    if (retryCount < RETRY_LIMIT) {
                        failedInstances.set(batchItem.id, {
                            ptr: batchItem.data.ptr,
                            lastFailTime: Date.now(),
                            retryCount: retryCount
                        });
                        console.log("[*] Added " + batchItem.id + " to retry queue (attempt " + retryCount + "/" + RETRY_LIMIT + ")");
                    } else {
                        console.log("[-] Instance " + batchItem.id + " exceeded retry limit, abandoning");
                    }
                    batchFailures++;
                }

                // Check if this is the last item in the batch
                if (index === batchInstances.length - 1) {
                    setTimeout(function() {
                        console.log("[+] Batch #" + batchNumber + " COMPLETED - Successes: " + batchSuccesses + ", Failures: " + batchFailures);
                        console.log("[*] Status - Processed: " + processedInstances.size + ", Pending: " + discoveredInstances.size + ", Failed: " + failedInstances.size);
                        isProcessingBatch = false;
                    }, 500);
                }
            }, index * 200); // 200ms delay between each instance in batch
        });
    }

    // Start batch processing timer
    setInterval(processCollectionBatch, BATCH_INTERVAL);

    // Status monitoring and cleanup
    function printBatchStatus() {
        console.log("=== BATCH PROCESSING STATUS ===");
        console.log("Discovered instances: " + discoveredInstances.size);
        console.log("Successfully processed: " + processedInstances.size);
        console.log("Failed instances (retry queue): " + failedInstances.size);
        console.log("Current batch number: " + batchNumber);
        console.log("Processing in progress: " + isProcessingBatch);

        if (failedInstances.size > 0) {
            console.log("Failed instances details:");
            failedInstances.forEach(function(data, instanceId) {
                console.log("  " + instanceId + " - Retries: " + data.retryCount + "/" + RETRY_LIMIT);
            });
        }
        console.log("===============================");
    }

    // Cleanup old processed instances (prevent memory bloat)
    function cleanupProcessedInstances() {
        if (processedInstances.size > 1000) {
            console.log("[*] Cleaning up old processed instances...");
            var processedArray = Array.from(processedInstances);
            // Keep only the most recent 500
            processedInstances.clear();
            for (var i = processedArray.length - 500; i < processedArray.length; i++) {
                if (i >= 0) {
                    processedInstances.add(processedArray[i]);
                }
            }
            console.log("[+] Cleaned up processed instances, kept " + processedInstances.size);
        }
    }

    // Status monitoring timer (every 30 seconds)
    setInterval(printBatchStatus, 30000);

    // Cleanup timer (every 5 minutes)
    setInterval(cleanupProcessedInstances, 300000);

    // Manual control functions (accessible via rpc.exports)
    rpc.exports = {
        getBatchStatus: printBatchStatus,
        clearProcessedInstances: function() {
            processedInstances.clear();
            console.log("[+] Cleared all processed instances");
        },
        clearFailedInstances: function() {
            failedInstances.clear();
            console.log("[+] Cleared all failed instances");
        },
        forceBatchProcess: function() {
            console.log("[*] Forcing immediate batch processing...");
            processCollectionBatch();
        },
        getStats: function() {
            return {
                discovered: discoveredInstances.size,
                processed: processedInstances.size,
                failed: failedInstances.size,
                batchNumber: batchNumber,
                isProcessing: isProcessingBatch
            };
        }
    };

    // Hook GetRewardType and GetRewardAmount for logging
    Interceptor.attach(il2cpp.base.add(0x209CC3C), {  // GetRewardType RVA
        onLeave: function(retval) {
            var rewardType = retval.toInt32();
            console.log("[*] Reward Type: " + rewardType);
        }
    });
    
    Interceptor.attach(il2cpp.base.add(0x209D9C4), {  // GetRewardAmount RVA
        onLeave: function(retval) {
            var amount = retval.toInt32();
            console.log("[*] Reward Amount: " + amount);
        }
    });
    
    console.log("[+] Ruins Auto-Collector hooks installed");
    console.log("[+] Will auto-collect any ruins when CanCollect() returns true");
});

// Helper function to manually trigger collection on specific entity
function manualCollect(goodyHutAddress) {
    var goodyHutPtr = ptr(goodyHutAddress);

    // Use the same enhanced module detection logic
    var il2cpp = null;
    var potentialModules = [];

    Process.enumerateModules().forEach(function(module) {
        var moduleName = module.name.toLowerCase();

        // Check for obvious patterns first
        if (moduleName.includes("il2cpp") ||
            moduleName.includes("unity") ||
            moduleName.includes("libmain") ||
            moduleName.includes("domination") ||
            moduleName.includes("nexon")) {
            il2cpp = module;
            return;
        }

        // Collect potential candidates
        if (module.size > 5000000) { // > 5MB
            potentialModules.push(module);
        }
    });

    // If no obvious module found, try the largest potential candidate
    if (!il2cpp && potentialModules.length > 0) {
        potentialModules.sort(function(a, b) { return b.size - a.size; });
        il2cpp = potentialModules[0];
        console.log("[*] Using largest module as IL2CPP candidate: " + il2cpp.name);
    }

    if (!il2cpp) {
        console.log("[-] IL2CPP module not found for manual collection");
        return false;
    }

    try {
        // Validate pointer first
        if (goodyHutPtr.isNull() || goodyHutPtr.equals(ptr(0))) {
            console.log("[-] Invalid GoodyHutHelper pointer: " + goodyHutAddress);
            return false;
        }

        var canCollectFunc = new NativeFunction(il2cpp.base.add(0x209D258), 'int', ['pointer']);
        var finishCollectFunc = new NativeFunction(il2cpp.base.add(0x209B924), 'void', ['pointer']);
        var configFunc = new NativeFunction(il2cpp.base.add(0x209B54C), 'pointer', ['pointer']);

        // Check if collectible with error handling
        var canCollect;
        try {
            canCollect = canCollectFunc(goodyHutPtr);
        } catch (e) {
            console.log("[-] Error calling CanCollect(): " + e);
            return false;
        }

        if (canCollect === 1) {
            // Safely set cleanUp flag
            var configPtr = null;
            try {
                configPtr = configFunc(goodyHutPtr);
                console.log("[*] Manual collection - Config() returned: " + configPtr);
            } catch (e) {
                console.log("[-] Error calling Config(): " + e);
                // Continue without setting cleanUp flag
            }

            if (configPtr && !configPtr.isNull() && !configPtr.equals(ptr(0))) {
                try {
                    // Validate memory access before writing
                    var cleanUpAddr = configPtr.add(0x30);
                    var currentValue = cleanUpAddr.readU8();
                    console.log("[*] Manual collection - Current cleanUp value: " + currentValue);

                    cleanUpAddr.writeU8(1);
                    console.log("[+] Manual collection - Set cleanUp flag");
                } catch (e) {
                    console.log("[-] Error setting cleanUp flag: " + e);
                    // Continue with collection anyway
                }
            } else {
                console.log("[-] Manual collection - Config() returned null/invalid pointer");
            }

            // Execute collection
            try {
                finishCollectFunc(goodyHutPtr);
                console.log("[+] Manual collection completed for: " + goodyHutAddress);
                return true;
            } catch (e) {
                console.log("[-] Error calling FinishCollect(): " + e);
                return false;
            }
        } else {
            console.log("[-] Cannot collect ruins at: " + goodyHutAddress + " (CanCollect returned: " + canCollect + ")");
            return false;
        }
    } catch (e) {
        console.log("[-] Error in manual collection: " + e);
        return false;
    }
}

// Usage: manualCollect("0x12345678")  // Replace with actual GoodyHutHelper instance address