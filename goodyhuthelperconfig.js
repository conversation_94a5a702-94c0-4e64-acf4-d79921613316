// Frida script for auto-collecting ruins in Unity IL2CPP game
// Based on GoodyHutHelper class analysis

Java.perform(function() {
    console.log("[+] Starting Ruins Auto-Collector");
    
    // Enhanced IL2CPP module detection for Dominations
    console.log("[*] Enumerating modules to find IL2CPP/Unity engine...");
    var il2cpp = null;
    var potentialModules = [];

    // System libraries to exclude (common Android system libs)
    var systemLibs = [
        "libc.so", "libm.so", "libdl.so", "liblog.so", "libz.so", "libstdc++.so",
        "libandroid.so", "libEGL.so", "libGLESv2.so", "libOpenSLES.so",
        "libvulkan.so", "libmediandk.so", "libaaudio.so", "libamidi.so",
        "libbinder.so", "libutils.so", "libcutils.so", "libhardware.so",
        "libui.so", "libgui.so", "libinput.so", "libsensor.so",
        "libcrypto.so", "libssl.so", "libicuuc.so", "libicui18n.so",
        "libsqlite.so", "libexpat.so", "libpng.so", "libjpeg.so",
        "libwebp.so", "libfreetype.so", "libharfbuzz.so", "libskia.so",
        "libft2.so", "libfontconfig.so", "libxml2.so", "libxslt.so",
        // Additional Android system libraries
        "audioclient-types-aidl-cpp.so", "libaaudio_internal.so", "libaudioclient.so",
        "libmedia.so", "libmediautils.so", "libstagefright.so", "libcamera2ndk.so",
        "libvndksupport.so", "libhidlbase.so", "libhidltransport.so",
        "libbluetooth.so", "libwifi-hal.so", "libril.so"
    ];

    // List all loaded modules with detailed analysis
    Process.enumerateModules().forEach(function(module) {
        var moduleName = module.name.toLowerCase();
        var moduleSize = module.size;
        var moduleBase = module.base;

        console.log("[*] Found module: " + module.name + " at " + module.base + " (size: " + moduleSize + ")");

        // Skip system libraries
        var isSystemLib = systemLibs.some(function(sysLib) {
            return moduleName === sysLib.toLowerCase();
        });

        if (isSystemLib) {
            return; // Skip system libraries
        }

        // Check for obvious IL2CPP/Unity patterns (highest priority)
        if (moduleName.includes("il2cpp") ||
            moduleName.includes("unity") ||
            moduleName.includes("libmain") ||
            moduleName.includes("libgame") ||
            moduleName.includes("libcocos") ||
            moduleName.includes("libue4") ||
            moduleName.includes("libengine")) {
            il2cpp = module;
            console.log("[+] FOUND IL2CPP module (pattern match): " + module.name);
            return;
        }

        // Check for Dominations-specific patterns (very high priority)
        if (moduleName.includes("domination") ||
            moduleName.includes("nexon") ||
            moduleName.includes("adk")) {
            potentialModules.push({module: module, reason: "dominations-specific name", priority: 1});
            console.log("[+] Potential IL2CPP module (dominations-specific): " + module.name);
        }

        // Check for generic game patterns (high priority, but only if very large)
        else if ((moduleName.includes("game") || moduleName.includes("client")) && moduleSize > 20000000) { // > 20MB
            potentialModules.push({module: module, reason: "game name + very large size", priority: 2});
            console.log("[+] Potential IL2CPP module (game-specific + large): " + module.name);
        }

        // Check for very large modules (likely game engine) - must be > 50MB
        else if (moduleSize > 50000000) { // > 50MB
            potentialModules.push({module: module, reason: "very large size (50MB+)", priority: 3});
            console.log("[+] Potential IL2CPP module (very large): " + module.name + " (" + (moduleSize/1024/1024).toFixed(1) + "MB)");
        }

        // Check for suspicious/obfuscated names (short, cryptic names) - must be very large
        else if (moduleName.match(/^lib[a-z0-9]{1,6}\.so$/) && moduleSize > 30000000) { // > 30MB
            potentialModules.push({module: module, reason: "suspicious name + very large size", priority: 4});
            console.log("[+] Potential IL2CPP module (suspicious + large): " + module.name + " (" + (moduleSize/1024/1024).toFixed(1) + "MB)");
        }

        // Log all non-system modules for debugging
        if (moduleSize > 1000000) { // > 1MB
            console.log("[DEBUG] Non-system module: " + module.name + " (" + (moduleSize/1024/1024).toFixed(1) + "MB) at " + moduleBase);
        }
    });

    // If no obvious IL2CPP module found, select best candidate
    if (!il2cpp && potentialModules.length > 0) {
        // Sort by priority (lower number = higher priority)
        potentialModules.sort(function(a, b) { return a.priority - b.priority; });

        il2cpp = potentialModules[0].module;
        console.log("[+] Selected best candidate IL2CPP module: " + il2cpp.name + " (reason: " + potentialModules[0].reason + ")");

        // Show all candidates for reference
        console.log("[*] All potential candidates:");
        potentialModules.forEach(function(candidate, index) {
            console.log("  " + (index + 1) + ". " + candidate.module.name + " - " + candidate.reason);
        });
    }
    
    if (!il2cpp) {
        console.log("[-] Could not find IL2CPP module automatically.");
        console.log("[-] Please check the DEBUG module list above and manually specify the correct module.");
        console.log("[-] Look for modules that are:");
        console.log("    1. Large in size (>20MB)");
        console.log("    2. Have game-related names");
        console.log("    3. Are NOT Android system libraries");
        console.log("[-] Use rpc.exports.setModule('module_name.so') to manually set the correct module");

        // Don't return - set up manual override capability
        il2cpp = {name: "MANUAL_OVERRIDE_NEEDED", base: ptr(0)};
    }
    
    console.log("[+] Using module: " + il2cpp.name + " at base: " + il2cpp.base);
    
    // Calculate actual addresses using module base + RVA offsets
    var canCollectAddr = il2cpp.base.add(0x209D258);  // CanCollect() RVA
    var finishCollectAddr = il2cpp.base.add(0x209B924);  // FinishCollect() RVA
    
    console.log("[*] CanCollect address: " + canCollectAddr);
    console.log("[*] FinishCollect address: " + finishCollectAddr);
    var stateMachineOffset = 0x18;  // m_stateMachine field offset
    
    // State constants
    var STATE_CLEANING_UP = "cleaning_up";
    
    // Hook CanCollect to auto-trigger collection
    Interceptor.attach(canCollectAddr, {
        onEnter: function(args) {
            this.thisPtr = this.context.x0;  // 'this' pointer (GoodyHutHelper instance)
            console.log("[*] CanCollect() called on GoodyHutHelper: " + this.thisPtr);
        },
        
        onLeave: function(retval) {
            var canCollect = retval.toInt32();
            console.log("[*] CanCollect() returned: " + canCollect);
            
            if (canCollect === 1) {  // true in IL2CPP
                console.log("[+] Ruins can be collected! Auto-triggering...");
                
                try {
                    // Call FinishCollect() on the same instance
                    var finishCollectFunc = new NativeFunction(finishCollectAddr, 'void', ['pointer']);
                    finishCollectFunc(this.thisPtr);
                    console.log("[+] FinishCollect() executed successfully");
                    
                    // Update state to STATE_CLEANING_UP
                    updateEntityState(this.thisPtr, STATE_CLEANING_UP);
                    
                } catch (e) {
                    console.log("[-] Error executing FinishCollect(): " + e);
                }
            }
        }
    });
    
    // Helper function to update entity state and trigger cleanup
    function updateEntityState(goodyHutPtr, newState) {
        try {
            // Get StateMachine pointer (offset 0x18)
            var stateMachinePtr = goodyHutPtr.add(stateMachineOffset).readPointer();

            if (!stateMachinePtr.isNull()) {
                console.log("[*] Updating state to: " + newState);

                // Find il2cpp_string_new to create state string
                var stringNewFunc = Module.findExportByName("libil2cpp.so", "il2cpp_string_new");
                if (stringNewFunc) {
                    var createString = new NativeFunction(stringNewFunc, 'pointer', ['pointer']);
                    var stateString = createString(Memory.allocUtf8String(newState));

                    // You would need to find the StateMachine's SetState method
                    // This is a simplified example - actual implementation depends on StateMachine structure
                    console.log("[+] State string created: " + stateString);
                }

                // If state is cleaning up, trigger automatic selling after a short delay
                if (newState === STATE_CLEANING_UP) {
                    setTimeout(function() {
                        try {
                            console.log("[*] Attempting automatic ruin selling...");
                            var sellRuinsAddr = il2cpp.base.add(0x209DE3C);  // SellRuins() RVA
                            var sellRuinsFunc = new NativeFunction(sellRuinsAddr, 'void', ['pointer']);
                            sellRuinsFunc(goodyHutPtr);
                            console.log("[+] SellRuins() executed for automatic cleanup");
                        } catch (e) {
                            console.log("[-] Error in automatic ruin selling: " + e);
                        }
                    }, 1000);  // 1 second delay
                }
            }
        } catch (e) {
            console.log("[-] Error updating state: " + e);
        }
    }
    
    // Two-Phase Batch Processing System for Ruins Collection
    var BATCH_SIZE = 15;  // Process 15 instances per batch
    var BATCH_INTERVAL = 3000;  // 3 seconds between batches
    var RETRY_LIMIT = 3;  // Maximum retry attempts per instance
    var DISCOVERY_TIMEOUT = 10000;  // 10 seconds without new discoveries to complete Phase 1

    // Phase tracking
    var currentPhase = 1;  // 1 = Discovery, 2 = Collection
    var lastDiscoveryTime = 0;
    var discoveryCompleteTime = 0;
    var totalDiscoveryScans = 0;

    // Data structures
    var discoveredInstances = new Map();  // instanceId -> {ptr, discoveryTime, retryCount}
    var processedInstances = new Set();   // Successfully processed instances
    var failedInstances = new Map();      // instanceId -> {ptr, lastFailTime, retryCount}
    var currentBatch = [];
    var batchNumber = 0;
    var isProcessingBatch = false;

    // Phase 1: Complete Discovery Scan
    var updateAddr = il2cpp.base.add(0x209CF60);  // Update() RVA
    var updateCallCount = 0;

    console.log("[+] ===== PHASE 1: DISCOVERY SCAN STARTED =====");
    console.log("[*] Scanning for ALL collectible GoodyHutHelper instances...");
    console.log("[*] Discovery will complete after 10 seconds with no new findings");

    Interceptor.attach(updateAddr, {
        onEnter: function(args) {
            updateCallCount++;
            totalDiscoveryScans++;

            // Phase 1: Discovery - check every 15 update calls (roughly 0.25 seconds at 60 FPS)
            if (currentPhase === 1 && updateCallCount % 15 === 0) {
                var thisPtr = this.context.x0;
                var instanceId = thisPtr.toString();

                try {
                    // Call CanCollect() to check if ready
                    var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                    var result = canCollectFunc(thisPtr);

                    if (result === 1) {
                        // Add to discovered instances if not already found
                        if (!discoveredInstances.has(instanceId)) {
                            discoveredInstances.set(instanceId, {
                                ptr: thisPtr,
                                discoveryTime: Date.now(),
                                retryCount: 0
                            });
                            lastDiscoveryTime = Date.now();
                            console.log("[+] PHASE 1: Discovered collectible ruins at: " + thisPtr + " (Total: " + discoveredInstances.size + ")");
                        }
                    }
                } catch (e) {
                    // Silently ignore errors during discovery to avoid spam
                }

                // Check if discovery phase should complete
                var now = Date.now();
                if (lastDiscoveryTime > 0 && (now - lastDiscoveryTime) > DISCOVERY_TIMEOUT) {
                    if (currentPhase === 1) {
                        completeDiscoveryPhase();
                    }
                }
            }

            // Phase 2: Collection processing happens in batch timer, not here
        }
    });

    // Function to complete discovery phase and start collection
    function completeDiscoveryPhase() {
        currentPhase = 2;
        discoveryCompleteTime = Date.now();

        console.log("[+] ===== PHASE 1: DISCOVERY SCAN COMPLETED =====");
        console.log("[+] Total collectible instances discovered: " + discoveredInstances.size);
        console.log("[+] Total discovery scans performed: " + totalDiscoveryScans);
        console.log("[+] Discovery phase duration: " + ((discoveryCompleteTime - lastDiscoveryTime + DISCOVERY_TIMEOUT) / 1000).toFixed(1) + " seconds");

        if (discoveredInstances.size === 0) {
            console.log("[*] No collectible ruins found. Monitoring will continue for new instances.");
        } else {
            console.log("[+] ===== PHASE 2: BATCH COLLECTION STARTING =====");
            console.log("[*] Beginning batch processing of " + discoveredInstances.size + " instances");
            console.log("[*] Batch size: " + BATCH_SIZE + ", Interval: " + (BATCH_INTERVAL/1000) + " seconds");

            // Start batch processing immediately
            setTimeout(function() {
                processCollectionBatch();
            }, 1000);
        }
    }

    // Initialize discovery phase
    lastDiscoveryTime = Date.now();

    // Phase 2: Batch processing function (only runs after discovery is complete)
    function processCollectionBatch() {
        // Only process batches in Phase 2
        if (currentPhase !== 2) {
            return;
        }

        if (isProcessingBatch) {
            console.log("[*] PHASE 2: Batch processing already in progress, skipping...");
            return;
        }

        isProcessingBatch = true;
        batchNumber++;

        // Prepare current batch from discovered instances and failed retries
        currentBatch = [];
        var batchInstances = [];

        // Add discovered instances to batch
        var discoveredArray = Array.from(discoveredInstances.entries());
        for (var i = 0; i < Math.min(BATCH_SIZE, discoveredArray.length); i++) {
            var [instanceId, data] = discoveredArray[i];
            batchInstances.push({id: instanceId, data: data, source: 'discovered'});
            discoveredInstances.delete(instanceId);
        }

        // Add failed instances ready for retry
        var now = Date.now();
        var failedArray = Array.from(failedInstances.entries());
        var remainingBatchSlots = BATCH_SIZE - batchInstances.length;

        for (var i = 0; i < Math.min(remainingBatchSlots, failedArray.length); i++) {
            var [instanceId, failData] = failedArray[i];
            // Retry after 10 seconds and if retry count is under limit
            if (now - failData.lastFailTime > 10000 && failData.retryCount < RETRY_LIMIT) {
                batchInstances.push({id: instanceId, data: failData, source: 'retry'});
                failedInstances.delete(instanceId);
            }
        }

        if (batchInstances.length === 0) {
            // Check if all processing is complete
            var totalRemaining = discoveredInstances.size + failedInstances.size;
            if (totalRemaining === 0) {
                console.log("[+] ===== PHASE 2: BATCH COLLECTION COMPLETED =====");
                console.log("[+] All discovered instances have been processed!");
                console.log("[+] Final Statistics:");
                console.log("    - Total processed: " + processedInstances.size);
                console.log("    - Total failed (abandoned): " + Array.from(failedInstances.values()).filter(f => f.retryCount >= RETRY_LIMIT).length);
                console.log("    - Total batches: " + batchNumber);
            } else {
                console.log("[*] PHASE 2: Batch #" + batchNumber + " - No instances ready to process");
                console.log("[*] Remaining: " + discoveredInstances.size + " discovered, " + failedInstances.size + " failed");
            }
            isProcessingBatch = false;
            return;
        }

        console.log("[+] PHASE 2: Starting Batch #" + batchNumber + " - Processing " + batchInstances.length + " instances");
        console.log("[*] Batch composition: " + batchInstances.filter(b => b.source === 'discovered').length + " discovered, " +
                   batchInstances.filter(b => b.source === 'retry').length + " retries");
        console.log("[*] Remaining after this batch: " + (discoveredInstances.size + failedInstances.size - batchInstances.length) + " instances");

        var batchSuccesses = 0;
        var batchFailures = 0;

        // Process each instance in the batch
        batchInstances.forEach(function(batchItem, index) {
            setTimeout(function() {
                try {
                    var instanceId = batchItem.id;
                    var instanceData = batchItem.data;
                    var thisPtr = instanceData.ptr;

                    console.log("[*] Batch #" + batchNumber + " [" + (index + 1) + "/" + batchInstances.length + "] Processing: " + instanceId);

                    // Validate pointer before any operations
                    if (thisPtr.isNull() || thisPtr.equals(ptr(0))) {
                        console.log("[-] Invalid thisPtr for instance " + instanceId + ", skipping");
                        batchFailures++;
                        return;
                    }

                    // Verify instance is still collectible with error handling
                    var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                    var canCollect;

                    try {
                        canCollect = canCollectFunc(thisPtr);
                    } catch (e) {
                        console.log("[-] Error calling CanCollect() for " + instanceId + ": " + e);
                        batchFailures++;
                        return;
                    }

                    if (canCollect !== 1) {
                        console.log("[*] Instance " + instanceId + " no longer collectible, skipping");
                        batchSuccesses++; // Count as success since it's no longer needed
                        return;
                    }

                    // Safely get config and set cleanUp flag
                    var configAddr = il2cpp.base.add(0x209B54C);  // Config() RVA
                    var configFunc = new NativeFunction(configAddr, 'pointer', ['pointer']);
                    var configPtr = null;

                    try {
                        configPtr = configFunc(thisPtr);
                        console.log("[*] Config() returned: " + configPtr + " for instance: " + instanceId);
                    } catch (e) {
                        console.log("[-] Error calling Config() for " + instanceId + ": " + e);
                        // Continue without setting cleanUp flag
                    }

                    if (configPtr && !configPtr.isNull() && !configPtr.equals(ptr(0))) {
                        try {
                            // Validate memory before writing
                            var cleanUpOffset = 0x30;
                            var cleanUpAddr = configPtr.add(cleanUpOffset);

                            // Try to read first to validate memory access
                            var currentValue = cleanUpAddr.readU8();
                            console.log("[*] Current cleanUp value: " + currentValue + " at " + cleanUpAddr);

                            // Set cleanUp flag to true
                            cleanUpAddr.writeU8(1);
                            console.log("[+] Set cleanUp flag for: " + instanceId);
                        } catch (e) {
                            console.log("[-] Error setting cleanUp flag for " + instanceId + ": " + e);
                            // Continue with collection anyway
                        }
                    } else {
                        console.log("[-] Warning: Config() returned null/invalid pointer for " + instanceId);
                    }

                    // Execute collection with error handling
                    var finishCollectFunc = new NativeFunction(finishCollectAddr, 'void', ['pointer']);
                    try {
                        finishCollectFunc(thisPtr);
                        console.log("[+] FinishCollect() executed for: " + instanceId);
                    } catch (e) {
                        console.log("[-] Error calling FinishCollect() for " + instanceId + ": " + e);
                        batchFailures++;
                        return;
                    }

                    // Mark as successfully processed
                    processedInstances.add(instanceId);
                    batchSuccesses++;
                    console.log("[+] Batch #" + batchNumber + " [" + (index + 1) + "/" + batchInstances.length + "] SUCCESS: " + instanceId);

                } catch (e) {
                    console.log("[-] Batch #" + batchNumber + " [" + (index + 1) + "/" + batchInstances.length + "] FAILED: " + batchItem.id + " - " + e);

                    // Add to failed instances for retry
                    var retryCount = (batchItem.data.retryCount || 0) + 1;
                    if (retryCount < RETRY_LIMIT) {
                        failedInstances.set(batchItem.id, {
                            ptr: batchItem.data.ptr,
                            lastFailTime: Date.now(),
                            retryCount: retryCount
                        });
                        console.log("[*] Added " + batchItem.id + " to retry queue (attempt " + retryCount + "/" + RETRY_LIMIT + ")");
                    } else {
                        console.log("[-] Instance " + batchItem.id + " exceeded retry limit, abandoning");
                    }
                    batchFailures++;
                }

                // Check if this is the last item in the batch
                if (index === batchInstances.length - 1) {
                    setTimeout(function() {
                        console.log("[+] Batch #" + batchNumber + " COMPLETED - Successes: " + batchSuccesses + ", Failures: " + batchFailures);
                        console.log("[*] Status - Processed: " + processedInstances.size + ", Pending: " + discoveredInstances.size + ", Failed: " + failedInstances.size);
                        isProcessingBatch = false;
                    }, 500);
                }
            }, index * 200); // 200ms delay between each instance in batch
        });
    }

    // Start batch processing timer (only runs in Phase 2)
    setInterval(function() {
        if (currentPhase === 2) {
            processCollectionBatch();
        }
    }, BATCH_INTERVAL);

    // Status monitoring and cleanup
    function printBatchStatus() {
        console.log("=== TWO-PHASE PROCESSING STATUS ===");
        console.log("Current Phase: " + (currentPhase === 1 ? "DISCOVERY" : "COLLECTION"));

        if (currentPhase === 1) {
            console.log("Discovery Progress:");
            console.log("  - Instances discovered: " + discoveredInstances.size);
            console.log("  - Total scans performed: " + totalDiscoveryScans);
            console.log("  - Last discovery: " + (lastDiscoveryTime > 0 ? ((Date.now() - lastDiscoveryTime) / 1000).toFixed(1) + "s ago" : "none"));
            console.log("  - Time until phase completion: " + (lastDiscoveryTime > 0 ? Math.max(0, (DISCOVERY_TIMEOUT - (Date.now() - lastDiscoveryTime)) / 1000).toFixed(1) + "s" : "waiting for first discovery"));
        } else {
            console.log("Collection Progress:");
            console.log("  - Discovered instances: " + discoveredInstances.size);
            console.log("  - Successfully processed: " + processedInstances.size);
            console.log("  - Failed instances (retry queue): " + failedInstances.size);
            console.log("  - Current batch number: " + batchNumber);
            console.log("  - Processing in progress: " + isProcessingBatch);

            if (failedInstances.size > 0) {
                console.log("Failed instances details:");
                failedInstances.forEach(function(data, instanceId) {
                    console.log("  " + instanceId + " - Retries: " + data.retryCount + "/" + RETRY_LIMIT);
                });
            }
        }
        console.log("===================================");
    }

    // Cleanup old processed instances (prevent memory bloat)
    function cleanupProcessedInstances() {
        if (processedInstances.size > 1000) {
            console.log("[*] Cleaning up old processed instances...");
            var processedArray = Array.from(processedInstances);
            // Keep only the most recent 500
            processedInstances.clear();
            for (var i = processedArray.length - 500; i < processedArray.length; i++) {
                if (i >= 0) {
                    processedInstances.add(processedArray[i]);
                }
            }
            console.log("[+] Cleaned up processed instances, kept " + processedInstances.size);
        }
    }

    // Status monitoring timer (every 30 seconds)
    setInterval(printBatchStatus, 30000);

    // Cleanup timer (every 5 minutes)
    setInterval(cleanupProcessedInstances, 300000);

    // Manual control functions (accessible via rpc.exports)
    rpc.exports = {
        getBatchStatus: printBatchStatus,
        clearProcessedInstances: function() {
            processedInstances.clear();
            console.log("[+] Cleared all processed instances");
        },
        clearFailedInstances: function() {
            failedInstances.clear();
            console.log("[+] Cleared all failed instances");
        },
        forceBatchProcess: function() {
            if (currentPhase === 2) {
                console.log("[*] Forcing immediate batch processing...");
                processCollectionBatch();
            } else {
                console.log("[*] Cannot force batch processing - still in discovery phase");
            }
        },
        forceCompleteDiscovery: function() {
            if (currentPhase === 1) {
                console.log("[*] Forcing discovery phase completion...");
                completeDiscoveryPhase();
            } else {
                console.log("[*] Discovery phase already completed");
            }
        },
        resetToDiscovery: function() {
            console.log("[*] Resetting to discovery phase...");
            currentPhase = 1;
            discoveredInstances.clear();
            processedInstances.clear();
            failedInstances.clear();
            batchNumber = 0;
            isProcessingBatch = false;
            lastDiscoveryTime = Date.now();
            totalDiscoveryScans = 0;
            console.log("[+] Reset complete - discovery phase restarted");
        },
        setModule: function(moduleName) {
            console.log("[*] Attempting to manually set IL2CPP module to: " + moduleName);

            var foundModule = null;
            Process.enumerateModules().forEach(function(module) {
                if (module.name === moduleName) {
                    foundModule = module;
                }
            });

            if (foundModule) {
                il2cpp = foundModule;

                // Recalculate addresses
                canCollectAddr = il2cpp.base.add(0x209D258);
                finishCollectAddr = il2cpp.base.add(0x209B924);

                console.log("[+] Successfully set IL2CPP module to: " + il2cpp.name);
                console.log("[+] Module base: " + il2cpp.base);
                console.log("[+] CanCollect address: " + canCollectAddr);
                console.log("[+] FinishCollect address: " + finishCollectAddr);
                console.log("[+] You can now restart the discovery phase with resetToDiscovery()");

                return true;
            } else {
                console.log("[-] Module '" + moduleName + "' not found");
                console.log("[-] Available modules:");
                Process.enumerateModules().forEach(function(module) {
                    if (module.size > 1000000) { // > 1MB
                        console.log("    " + module.name + " (" + (module.size/1024/1024).toFixed(1) + "MB)");
                    }
                });
                return false;
            }
        },
        listLargeModules: function() {
            console.log("[*] Large modules (>1MB) in process:");
            var modules = [];
            Process.enumerateModules().forEach(function(module) {
                if (module.size > 1000000) { // > 1MB
                    modules.push({
                        name: module.name,
                        size: module.size,
                        sizeMB: (module.size/1024/1024).toFixed(1),
                        base: module.base.toString()
                    });
                }
            });

            // Sort by size (largest first)
            modules.sort(function(a, b) { return b.size - a.size; });

            modules.forEach(function(mod, index) {
                console.log("  " + (index + 1) + ". " + mod.name + " (" + mod.sizeMB + "MB) at " + mod.base);
            });

            return modules;
        },
        getStats: function() {
            return {
                phase: currentPhase,
                discovered: discoveredInstances.size,
                processed: processedInstances.size,
                failed: failedInstances.size,
                batchNumber: batchNumber,
                isProcessing: isProcessingBatch,
                totalScans: totalDiscoveryScans,
                lastDiscovery: lastDiscoveryTime,
                discoveryComplete: discoveryCompleteTime
            };
        }
    };

    // Hook GetRewardType and GetRewardAmount for logging
    Interceptor.attach(il2cpp.base.add(0x209CC3C), {  // GetRewardType RVA
        onLeave: function(retval) {
            var rewardType = retval.toInt32();
            console.log("[*] Reward Type: " + rewardType);
        }
    });
    
    Interceptor.attach(il2cpp.base.add(0x209D9C4), {  // GetRewardAmount RVA
        onLeave: function(retval) {
            var amount = retval.toInt32();
            console.log("[*] Reward Amount: " + amount);
        }
    });
    
    console.log("[+] Ruins Auto-Collector hooks installed");
    console.log("[+] Will auto-collect any ruins when CanCollect() returns true");
});

// Helper function to manually trigger collection on specific entity
function manualCollect(goodyHutAddress) {
    var goodyHutPtr = ptr(goodyHutAddress);

    // Use the same enhanced module detection logic
    var il2cpp = null;
    var potentialModules = [];

    Process.enumerateModules().forEach(function(module) {
        var moduleName = module.name.toLowerCase();

        // Check for obvious patterns first
        if (moduleName.includes("il2cpp") ||
            moduleName.includes("unity") ||
            moduleName.includes("libmain") ||
            moduleName.includes("domination") ||
            moduleName.includes("nexon")) {
            il2cpp = module;
            return;
        }

        // Collect potential candidates
        if (module.size > 5000000) { // > 5MB
            potentialModules.push(module);
        }
    });

    // If no obvious module found, try the largest potential candidate
    if (!il2cpp && potentialModules.length > 0) {
        potentialModules.sort(function(a, b) { return b.size - a.size; });
        il2cpp = potentialModules[0];
        console.log("[*] Using largest module as IL2CPP candidate: " + il2cpp.name);
    }

    if (!il2cpp) {
        console.log("[-] IL2CPP module not found for manual collection");
        return false;
    }

    try {
        // Validate pointer first
        if (goodyHutPtr.isNull() || goodyHutPtr.equals(ptr(0))) {
            console.log("[-] Invalid GoodyHutHelper pointer: " + goodyHutAddress);
            return false;
        }

        var canCollectFunc = new NativeFunction(il2cpp.base.add(0x209D258), 'int', ['pointer']);
        var finishCollectFunc = new NativeFunction(il2cpp.base.add(0x209B924), 'void', ['pointer']);
        var configFunc = new NativeFunction(il2cpp.base.add(0x209B54C), 'pointer', ['pointer']);

        // Check if collectible with error handling
        var canCollect;
        try {
            canCollect = canCollectFunc(goodyHutPtr);
        } catch (e) {
            console.log("[-] Error calling CanCollect(): " + e);
            return false;
        }

        if (canCollect === 1) {
            // Safely set cleanUp flag
            var configPtr = null;
            try {
                configPtr = configFunc(goodyHutPtr);
                console.log("[*] Manual collection - Config() returned: " + configPtr);
            } catch (e) {
                console.log("[-] Error calling Config(): " + e);
                // Continue without setting cleanUp flag
            }

            if (configPtr && !configPtr.isNull() && !configPtr.equals(ptr(0))) {
                try {
                    // Validate memory access before writing
                    var cleanUpAddr = configPtr.add(0x30);
                    var currentValue = cleanUpAddr.readU8();
                    console.log("[*] Manual collection - Current cleanUp value: " + currentValue);

                    cleanUpAddr.writeU8(1);
                    console.log("[+] Manual collection - Set cleanUp flag");
                } catch (e) {
                    console.log("[-] Error setting cleanUp flag: " + e);
                    // Continue with collection anyway
                }
            } else {
                console.log("[-] Manual collection - Config() returned null/invalid pointer");
            }

            // Execute collection
            try {
                finishCollectFunc(goodyHutPtr);
                console.log("[+] Manual collection completed for: " + goodyHutAddress);
                return true;
            } catch (e) {
                console.log("[-] Error calling FinishCollect(): " + e);
                return false;
            }
        } else {
            console.log("[-] Cannot collect ruins at: " + goodyHutAddress + " (CanCollect returned: " + canCollect + ")");
            return false;
        }
    } catch (e) {
        console.log("[-] Error in manual collection: " + e);
        return false;
    }
}

// Usage: manualCollect("0x12345678")  // Replace with actual GoodyHutHelper instance address