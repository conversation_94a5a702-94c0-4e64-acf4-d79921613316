// Frida script for auto-collecting ruins in Unity IL2CPP game
// Based on GoodyHutHelper class analysis

Java.perform(function() {
    console.log("[+] Starting Ruins Auto-Collector");
    
    // Wait for libil2cpp.so to load
    console.log("[*] Waiting for libil2cpp.so to load...");
    var il2cpp = null;
    var moduleCheckInterval = null;
    var maxWaitTime = 60000; // 60 seconds maximum wait
    var startTime = Date.now();

    function checkForIL2CPP() {
        Process.enumerateModules().forEach(function(module) {
            if (module.name === "libil2cpp.so") {
                il2cpp = module;
                console.log("[+] Found libil2cpp.so at: " + module.base + " (size: " + (module.size/1024/1024).toFixed(1) + "MB)");
                return;
            }
        });

        if (il2cpp) {
            clearInterval(moduleCheckInterval);
            console.log("[+] libil2cpp.so loaded successfully!");
            initializeHooks();
        } else {
            var elapsed = Date.now() - startTime;
            if (elapsed > maxWaitTime) {
                clearInterval(moduleCheckInterval);
                console.log("[-] Timeout waiting for libil2cpp.so to load after " + (maxWaitTime/1000) + " seconds");
                console.log("[-] Available modules:");
                Process.enumerateModules().forEach(function(module) {
                    if (module.size > 5000000) { // > 5MB
                        console.log("    " + module.name + " (" + (module.size/1024/1024).toFixed(1) + "MB)");
                    }
                });
                console.log("[-] Use rpc.exports.setModule('module_name.so') to manually set the correct module");
                return;
            }

            if (elapsed % 5000 < 500) { // Log every 5 seconds
                console.log("[*] Still waiting for libil2cpp.so... (" + (elapsed/1000).toFixed(1) + "s elapsed)");
            }
        }
    }

    // Check immediately
    checkForIL2CPP();

    // If not found, check every 500ms
    if (!il2cpp) {
        moduleCheckInterval = setInterval(checkForIL2CPP, 500);
    } else {
        initializeHooks();
    }

    function initializeHooks() {
        if (!il2cpp || il2cpp.name === "MANUAL_OVERRIDE_NEEDED") {
            console.log("[-] Cannot initialize hooks - no valid IL2CPP module");
            return;
        }

        console.log("[+] Using module: " + il2cpp.name + " at base: " + il2cpp.base);

        // Calculate actual addresses using module base + RVA offsets
        var canCollectAddr = il2cpp.base.add(0x209D258);  // CanCollect() RVA
        var finishCollectAddr = il2cpp.base.add(0x209B924);  // FinishCollect() RVA

        console.log("[*] CanCollect address: " + canCollectAddr);
        console.log("[*] FinishCollect address: " + finishCollectAddr);
    var stateMachineOffset = 0x18;  // m_stateMachine field offset
    
    // State constants
    var STATE_CLEANING_UP = "cleaning_up";
    
    // Hook CanCollect to auto-trigger collection
    Interceptor.attach(canCollectAddr, {
        onEnter: function(args) {
            this.thisPtr = this.context.x0;  // 'this' pointer (GoodyHutHelper instance)
            console.log("[*] CanCollect() called on GoodyHutHelper: " + this.thisPtr);
        },
        
        onLeave: function(retval) {
            var canCollect = retval.toInt32();
            console.log("[*] CanCollect() returned: " + canCollect);
            
            if (canCollect === 1) {  // true in IL2CPP
                console.log("[+] Ruins can be collected! Auto-triggering...");
                
                try {
                    // Call FinishCollect() on the same instance
                    var finishCollectFunc = new NativeFunction(finishCollectAddr, 'void', ['pointer']);
                    finishCollectFunc(this.thisPtr);
                    console.log("[+] FinishCollect() executed successfully");
                    
                    // Update state to STATE_CLEANING_UP
                    updateEntityState(this.thisPtr, STATE_CLEANING_UP);
                    
                } catch (e) {
                    console.log("[-] Error executing FinishCollect(): " + e);
                }
            }
        }
    });
    
    // Helper function to update entity state and trigger cleanup
    function updateEntityState(goodyHutPtr, newState) {
        try {
            // Get StateMachine pointer (offset 0x18)
            var stateMachinePtr = goodyHutPtr.add(stateMachineOffset).readPointer();

            if (!stateMachinePtr.isNull()) {
                console.log("[*] Updating state to: " + newState);

                // Find il2cpp_string_new to create state string
                var stringNewFunc = Module.findExportByName("libil2cpp.so", "il2cpp_string_new");
                if (stringNewFunc) {
                    var createString = new NativeFunction(stringNewFunc, 'pointer', ['pointer']);
                    var stateString = createString(Memory.allocUtf8String(newState));

                    // You would need to find the StateMachine's SetState method
                    // This is a simplified example - actual implementation depends on StateMachine structure
                    console.log("[+] State string created: " + stateString);
                }

                // If state is cleaning up, trigger automatic selling after a short delay
                if (newState === STATE_CLEANING_UP) {
                    setTimeout(function() {
                        try {
                            console.log("[*] Attempting automatic ruin selling...");
                            var sellRuinsAddr = il2cpp.base.add(0x209DE3C);  // SellRuins() RVA
                            var sellRuinsFunc = new NativeFunction(sellRuinsAddr, 'void', ['pointer']);
                            sellRuinsFunc(goodyHutPtr);
                            console.log("[+] SellRuins() executed for automatic cleanup");
                        } catch (e) {
                            console.log("[-] Error in automatic ruin selling: " + e);
                        }
                    }, 1000);  // 1 second delay
                }
            }
        } catch (e) {
            console.log("[-] Error updating state: " + e);
        }
    }
    
    // Two-Phase Batch Processing System for Ruins Collection
    var BATCH_SIZE = 15;  // Process 15 instances per batch
    var BATCH_INTERVAL = 3000;  // 3 seconds between batches
    var RETRY_LIMIT = 3;  // Maximum retry attempts per instance
    var DISCOVERY_TIMEOUT = 10000;  // 10 seconds without new discoveries to complete Phase 1

    // Phase tracking
    var currentPhase = 1;  // 1 = Discovery, 2 = Collection
    var lastDiscoveryTime = 0;
    var discoveryCompleteTime = 0;
    var totalDiscoveryScans = 0;

    // Data structures
    var discoveredInstances = new Map();  // instanceId -> {ptr, discoveryTime, retryCount}
    var processedInstances = new Set();   // Successfully processed instances
    var failedInstances = new Map();      // instanceId -> {ptr, lastFailTime, retryCount}
    var currentBatch = [];
    var batchNumber = 0;
    var isProcessingBatch = false;

    // Phase 1: Complete Discovery Scan
    var updateAddr = il2cpp.base.add(0x209CF60);  // Update() RVA
    var updateCallCount = 0;

    console.log("[+] ===== PHASE 1: DISCOVERY SCAN STARTED =====");
    console.log("[*] Scanning for ALL collectible GoodyHutHelper instances...");
    console.log("[*] Discovery will complete after 10 seconds with no new findings");

    Interceptor.attach(updateAddr, {
        onEnter: function(args) {
            updateCallCount++;
            totalDiscoveryScans++;

            // DISCOVERY ONLY: Just scan and log collectible ruins (no auto-collection)
            if (updateCallCount % 15 === 0) {
                var thisPtr = this.context.x0;
                var instanceId = thisPtr.toString();

                try {
                    // Call CanCollect() to check if ready
                    var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                    var result = canCollectFunc(thisPtr);

                    if (result === 1) {
                        // Just discover and log, don't auto-collect
                        if (!discoveredInstances.has(instanceId)) {
                            discoveredInstances.set(instanceId, {
                                ptr: thisPtr,
                                discoveryTime: Date.now(),
                                retryCount: 0
                            });
                            console.log("[*] DISCOVERED: Collectible ruins at: " + thisPtr + " (Total discovered: " + discoveredInstances.size + ")");
                        }
                    }
                } catch (e) {
                    // Silently ignore errors during discovery to avoid spam
                }
            }

            // Phase 2: Collection processing happens in batch timer, not here
        }
    });

    // Function to complete discovery phase and start collection
    function completeDiscoveryPhase() {
        currentPhase = 2;
        discoveryCompleteTime = Date.now();

        console.log("[+] ===== PHASE 1: DISCOVERY SCAN COMPLETED =====");
        console.log("[+] Total collectible instances discovered: " + discoveredInstances.size);
        console.log("[+] Total discovery scans performed: " + totalDiscoveryScans);
        console.log("[+] Discovery phase duration: " + ((discoveryCompleteTime - lastDiscoveryTime + DISCOVERY_TIMEOUT) / 1000).toFixed(1) + " seconds");

        if (discoveredInstances.size === 0) {
            console.log("[*] No collectible ruins found. Monitoring will continue for new instances.");
        } else {
            console.log("[+] ===== PHASE 2: BATCH COLLECTION STARTING =====");
            console.log("[*] Beginning batch processing of " + discoveredInstances.size + " instances");
            console.log("[*] Batch size: " + BATCH_SIZE + ", Interval: " + (BATCH_INTERVAL/1000) + " seconds");

            // Start batch processing immediately
            setTimeout(function() {
                processCollectionBatch();
            }, 1000);
        }
    }

    // Initialize discovery phase
    lastDiscoveryTime = Date.now();

    // Phase 2: Batch processing function (only runs after discovery is complete)
    function processCollectionBatch() {
        // Only process batches in Phase 2
        if (currentPhase !== 2) {
            return;
        }

        if (isProcessingBatch) {
            console.log("[*] PHASE 2: Batch processing already in progress, skipping...");
            return;
        }

        isProcessingBatch = true;
        batchNumber++;

        // Prepare current batch from discovered instances and failed retries
        currentBatch = [];
        var batchInstances = [];

        // Add discovered instances to batch
        var discoveredArray = Array.from(discoveredInstances.entries());
        for (var i = 0; i < Math.min(BATCH_SIZE, discoveredArray.length); i++) {
            var [instanceId, data] = discoveredArray[i];
            batchInstances.push({id: instanceId, data: data, source: 'discovered'});
            discoveredInstances.delete(instanceId);
        }

        // Add failed instances ready for retry
        var now = Date.now();
        var failedArray = Array.from(failedInstances.entries());
        var remainingBatchSlots = BATCH_SIZE - batchInstances.length;

        for (var i = 0; i < Math.min(remainingBatchSlots, failedArray.length); i++) {
            var [instanceId, failData] = failedArray[i];
            // Retry after 10 seconds and if retry count is under limit
            if (now - failData.lastFailTime > 10000 && failData.retryCount < RETRY_LIMIT) {
                batchInstances.push({id: instanceId, data: failData, source: 'retry'});
                failedInstances.delete(instanceId);
            }
        }

        if (batchInstances.length === 0) {
            // Check if all processing is complete
            var totalRemaining = discoveredInstances.size + failedInstances.size;
            if (totalRemaining === 0) {
                console.log("[+] ===== PHASE 2: BATCH COLLECTION COMPLETED =====");
                console.log("[+] All discovered instances have been processed!");
                console.log("[+] Final Statistics:");
                console.log("    - Total processed: " + processedInstances.size);
                console.log("    - Total failed (abandoned): " + Array.from(failedInstances.values()).filter(f => f.retryCount >= RETRY_LIMIT).length);
                console.log("    - Total batches: " + batchNumber);
            } else {
                console.log("[*] PHASE 2: Batch #" + batchNumber + " - No instances ready to process");
                console.log("[*] Remaining: " + discoveredInstances.size + " discovered, " + failedInstances.size + " failed");
            }
            isProcessingBatch = false;
            return;
        }

        console.log("[+] PHASE 2: Starting Batch #" + batchNumber + " - Processing " + batchInstances.length + " instances");
        console.log("[*] Batch composition: " + batchInstances.filter(b => b.source === 'discovered').length + " discovered, " +
                   batchInstances.filter(b => b.source === 'retry').length + " retries");
        console.log("[*] Remaining after this batch: " + (discoveredInstances.size + failedInstances.size - batchInstances.length) + " instances");

        var batchSuccesses = 0;
        var batchFailures = 0;

        // Process each instance in the batch
        batchInstances.forEach(function(batchItem, index) {
            setTimeout(function() {
                try {
                    var instanceId = batchItem.id;
                    var instanceData = batchItem.data;
                    var thisPtr = instanceData.ptr;

                    console.log("[*] Batch #" + batchNumber + " [" + (index + 1) + "/" + batchInstances.length + "] Processing: " + instanceId);

                    // CRITICAL: Re-discover this instance in real-time instead of using stale pointer
                    var freshPtr = null;
                    var freshInstanceFound = false;

                    console.log("[*] Re-scanning for fresh instance pointer (stale ptr was: " + thisPtr + ")");

                    // Use the Update() hook mechanism to find a fresh pointer for this instance
                    var tempDiscoveryCount = 0;
                    var maxDiscoveryAttempts = 100; // Limit discovery attempts

                    // Temporarily hook Update() to find fresh instances
                    var tempUpdateHook = Interceptor.attach(updateAddr, {
                        onEnter: function(args) {
                            if (freshInstanceFound || tempDiscoveryCount >= maxDiscoveryAttempts) {
                                return;
                            }

                            tempDiscoveryCount++;
                            var currentPtr = this.context.x0;

                            try {
                                var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                                var result = canCollectFunc(currentPtr);

                                if (result === 1) {
                                    // Found a collectible instance - use this fresh pointer
                                    freshPtr = currentPtr;
                                    freshInstanceFound = true;
                                    console.log("[+] Found fresh collectible instance at: " + freshPtr);
                                }
                            } catch (e) {
                                // Ignore errors during fresh discovery
                            }
                        }
                    });

                    // Wait a moment for fresh discovery
                    setTimeout(function() {
                        tempUpdateHook.detach();

                        if (!freshInstanceFound || !freshPtr) {
                            console.log("[-] Could not find fresh instance pointer for " + instanceId + " after " + tempDiscoveryCount + " attempts");
                            batchFailures++;
                            return;
                        }

                        // Use the fresh pointer for processing
                        thisPtr = freshPtr;
                        console.log("[+] Using fresh pointer: " + thisPtr + " (discovered in " + tempDiscoveryCount + " attempts)");

                        // Validate fresh pointer
                        if (thisPtr.isNull() || thisPtr.equals(ptr(0))) {
                            console.log("[-] Fresh pointer is invalid for instance " + instanceId);
                            batchFailures++;
                            return;
                        }

                        // Verify fresh instance is still collectible
                        var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                        var canCollect;

                        try {
                            canCollect = canCollectFunc(thisPtr);
                            console.log("[*] Fresh instance CanCollect() returned: " + canCollect);
                        } catch (e) {
                            console.log("[-] Error calling CanCollect() on fresh pointer " + thisPtr + ": " + e);
                            batchFailures++;
                            return;
                        }

                        if (canCollect !== 1) {
                            console.log("[*] Fresh instance no longer collectible, skipping");
                            batchSuccesses++; // Count as success since it's no longer needed
                            return;
                        }

                        // Safely get config and set cleanUp flag using fresh pointer
                        var configAddr = il2cpp.base.add(0x209B54C);  // Config() RVA
                        var configFunc = new NativeFunction(configAddr, 'pointer', ['pointer']);
                        var configPtr = null;

                        try {
                            configPtr = configFunc(thisPtr);
                            console.log("[*] Config() returned: " + configPtr + " for fresh instance");
                        } catch (e) {
                            console.log("[-] Error calling Config() on fresh pointer: " + e);
                            // Continue without setting cleanUp flag
                        }

                        if (configPtr && !configPtr.isNull() && !configPtr.equals(ptr(0))) {
                            try {
                                // Validate memory before writing
                                var cleanUpOffset = 0x30;
                                var cleanUpAddr = configPtr.add(cleanUpOffset);

                                // Try to read first to validate memory access
                                var currentValue = cleanUpAddr.readU8();
                                console.log("[*] Current cleanUp value: " + currentValue + " at " + cleanUpAddr);

                                // Set cleanUp flag to true
                                cleanUpAddr.writeU8(1);
                                console.log("[+] Set cleanUp flag for fresh instance");
                            } catch (e) {
                                console.log("[-] Error setting cleanUp flag: " + e);
                                // Continue with collection anyway
                            }
                        } else {
                            console.log("[-] Warning: Config() returned null/invalid pointer for fresh instance");
                        }

                        // Execute collection with error handling using fresh pointer
                        var finishCollectFunc = new NativeFunction(finishCollectAddr, 'void', ['pointer']);
                        try {
                            finishCollectFunc(thisPtr);
                            console.log("[+] FinishCollect() executed successfully on fresh pointer");
                        } catch (e) {
                            console.log("[-] Error calling FinishCollect() on fresh pointer: " + e);
                            batchFailures++;
                            return;
                        }

                        // Mark as successfully processed
                        processedInstances.add(instanceId);
                        batchSuccesses++;
                        console.log("[+] Batch #" + batchNumber + " [" + (index + 1) + "/" + batchInstances.length + "] SUCCESS: " + instanceId);

                    }, 1000); // Wait 1 second for fresh discovery

                } catch (e) {
                    console.log("[-] Batch #" + batchNumber + " [" + (index + 1) + "/" + batchInstances.length + "] FAILED: " + batchItem.id + " - " + e);

                    // Add to failed instances for retry
                    var retryCount = (batchItem.data.retryCount || 0) + 1;
                    if (retryCount < RETRY_LIMIT) {
                        failedInstances.set(batchItem.id, {
                            ptr: batchItem.data.ptr,
                            lastFailTime: Date.now(),
                            retryCount: retryCount
                        });
                        console.log("[*] Added " + batchItem.id + " to retry queue (attempt " + retryCount + "/" + RETRY_LIMIT + ")");
                    } else {
                        console.log("[-] Instance " + batchItem.id + " exceeded retry limit, abandoning");
                    }
                    batchFailures++;
                }

                // Check if this is the last item in the batch
                if (index === batchInstances.length - 1) {
                    setTimeout(function() {
                        console.log("[+] Batch #" + batchNumber + " COMPLETED - Successes: " + batchSuccesses + ", Failures: " + batchFailures);
                        console.log("[*] Status - Processed: " + processedInstances.size + ", Pending: " + discoveredInstances.size + ", Failed: " + failedInstances.size);
                        isProcessingBatch = false;
                    }, 500);
                }
            }, index * 200); // 200ms delay between each instance in batch
        });
    }

    // Start batch processing timer (only runs in Phase 2)
    setInterval(function() {
        if (currentPhase === 2) {
            processCollectionBatch();
        }
    }, BATCH_INTERVAL);

    // Status monitoring and cleanup
    function printBatchStatus() {
        console.log("=== DISCOVERY MODE STATUS ===");
        console.log("Collection Mode: DISCOVERY ONLY (manual collection required)");
        console.log("Progress:");
        console.log("  - Discovered collectible ruins: " + discoveredInstances.size);
        console.log("  - Successfully processed: " + processedInstances.size);
        console.log("  - Failed instances: " + failedInstances.size);
        console.log("  - Total scans performed: " + totalDiscoveryScans);

        if (failedInstances.size > 0) {
            console.log("Failed instances details:");
            failedInstances.forEach(function(data, instanceId) {
                console.log("  " + instanceId + " - Retries: " + data.retryCount + "/" + RETRY_LIMIT);
            });
        }

        if (discoveredInstances.size > 0) {
            console.log("[*] " + discoveredInstances.size + " ruins ready for manual collection");
            console.log("[*] Use rpc.exports.collectDiscoveredRuins() to collect them");
        } else {
            console.log("[*] No collectible ruins currently discovered");
        }
        console.log("==============================");
    }

    // Cleanup old processed instances (prevent memory bloat)
    function cleanupProcessedInstances() {
        if (processedInstances.size > 1000) {
            console.log("[*] Cleaning up old processed instances...");
            var processedArray = Array.from(processedInstances);
            // Keep only the most recent 500
            processedInstances.clear();
            for (var i = processedArray.length - 500; i < processedArray.length; i++) {
                if (i >= 0) {
                    processedInstances.add(processedArray[i]);
                }
            }
            console.log("[+] Cleaned up processed instances, kept " + processedInstances.size);
        }
    }

    // Status monitoring timer (every 30 seconds)
    setInterval(printBatchStatus, 30000);

    // Cleanup timer (every 5 minutes)
    setInterval(cleanupProcessedInstances, 300000);

    // Manual control functions (accessible via rpc.exports)
    rpc.exports = {
        getBatchStatus: printBatchStatus,
        clearProcessedInstances: function() {
            processedInstances.clear();
            console.log("[+] Cleared all processed instances");
        },
        clearFailedInstances: function() {
            failedInstances.clear();
            console.log("[+] Cleared all failed instances");
        },
        forceBatchProcess: function() {
            if (currentPhase === 2) {
                console.log("[*] Forcing immediate batch processing...");
                processCollectionBatch();
            } else {
                console.log("[*] Cannot force batch processing - still in discovery phase");
            }
        },
        forceCompleteDiscovery: function() {
            if (currentPhase === 1) {
                console.log("[*] Forcing discovery phase completion...");
                completeDiscoveryPhase();
            } else {
                console.log("[*] Discovery phase already completed");
            }
        },
        resetToDiscovery: function() {
            console.log("[*] Resetting to discovery phase...");
            currentPhase = 1;
            discoveredInstances.clear();
            processedInstances.clear();
            failedInstances.clear();
            batchNumber = 0;
            isProcessingBatch = false;
            lastDiscoveryTime = Date.now();
            totalDiscoveryScans = 0;
            console.log("[+] Reset complete - discovery phase restarted");
        },
        setModule: function(moduleName) {
            console.log("[*] Attempting to manually set IL2CPP module to: " + moduleName);

            var foundModule = null;
            Process.enumerateModules().forEach(function(module) {
                if (module.name === moduleName) {
                    foundModule = module;
                }
            });

            if (foundModule) {
                il2cpp = foundModule;
                console.log("[+] Successfully set IL2CPP module to: " + il2cpp.name);
                console.log("[+] Module base: " + il2cpp.base);
                console.log("[+] Reinitializing hooks with new module...");

                // Reinitialize hooks with the new module
                initializeHooks();

                return true;
            } else {
                console.log("[-] Module '" + moduleName + "' not found");
                console.log("[-] Available modules:");
                Process.enumerateModules().forEach(function(module) {
                    if (module.size > 1000000) { // > 1MB
                        console.log("    " + module.name + " (" + (module.size/1024/1024).toFixed(1) + "MB)");
                    }
                });
                return false;
            }
        },
        listLargeModules: function() {
            console.log("[*] Large modules (>1MB) in process:");
            var modules = [];
            Process.enumerateModules().forEach(function(module) {
                if (module.size > 1000000) { // > 1MB
                    modules.push({
                        name: module.name,
                        size: module.size,
                        sizeMB: (module.size/1024/1024).toFixed(1),
                        base: module.base.toString()
                    });
                }
            });

            // Sort by size (largest first)
            modules.sort(function(a, b) { return b.size - a.size; });

            modules.forEach(function(mod, index) {
                console.log("  " + (index + 1) + ". " + mod.name + " (" + mod.sizeMB + "MB) at " + mod.base);
            });

            return modules;
        },
        diagnosticScan: function() {
            console.log("[+] ===== DIAGNOSTIC SCAN =====");
            console.log("[*] Analyzing ALL GoodyHutHelper instances...");

            var scannedCount = 0;
            var collectibleRuins = 0;
            var collectedRuins = 0;
            var errorRuins = 0;
            var healthyRuins = 0;
            var zeroHealthRuins = 0;
            var inCooldownRuins = 0;
            var examples = [];

            // Diagnostic scan
            var diagHook = Interceptor.attach(updateAddr, {
                onEnter: function(args) {
                    scannedCount++;

                    // Limit scanning
                    if (scannedCount > 200) {
                        return;
                    }

                    var thisPtr = this.context.x0;

                    try {
                        // Get health first
                        var getHealthFunc = new NativeFunction(il2cpp.base.add(0x209EA1C), 'int', ['pointer']);
                        var health = getHealthFunc(thisPtr);

                        if (health > 0) {
                            healthyRuins++;
                        } else {
                            zeroHealthRuins++;
                        }

                        // Try CanCollect
                        var canCollect = null;
                        var canCollectError = false;
                        try {
                            var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                            canCollect = canCollectFunc(thisPtr);
                        } catch (e) {
                            canCollectError = true;
                            errorRuins++;
                        }

                        // Get cooldown
                        var getCooldownFunc = new NativeFunction(il2cpp.base.add(0x209E9B0), 'double', ['pointer']);
                        var cooldownTime = getCooldownFunc(thisPtr);

                        if (cooldownTime > 0) {
                            inCooldownRuins++;
                        }

                        // Categorize
                        var status = "";
                        if (canCollect === 1) {
                            collectibleRuins++;
                            status = "COLLECTIBLE";
                        } else if (canCollect === 0) {
                            collectedRuins++;
                            status = "COLLECTED";
                        } else if (canCollectError) {
                            collectedRuins++;
                            status = "COLLECTED (ERROR)";
                        }

                        // Store examples
                        if (examples.length < 10) {
                            examples.push({
                                ptr: thisPtr.toString(),
                                health: health,
                                canCollect: canCollectError ? "ERROR" : canCollect,
                                cooldown: cooldownTime.toFixed(1),
                                status: status
                            });
                        }

                        // Log first few for immediate feedback
                        if (scannedCount <= 10) {
                            console.log("[*] Instance " + scannedCount + ": " + thisPtr + " - Health: " + health + ", CanCollect: " + (canCollectError ? "ERROR" : canCollect) + ", Cooldown: " + cooldownTime.toFixed(1) + "s, Status: " + status);
                        }

                    } catch (e) {
                        console.log("[-] Error scanning instance " + scannedCount + ": " + e);
                    }
                }
            });

            // Wait for scanning to complete
            setTimeout(function() {
                diagHook.detach();

                console.log("[+] ===== DIAGNOSTIC RESULTS =====");
                console.log("[+] Total instances scanned: " + scannedCount);
                console.log("[+] Breakdown:");
                console.log("    - Collectible ruins (CanCollect=1): " + collectibleRuins);
                console.log("    - Collected ruins (CanCollect=0): " + collectedRuins);
                console.log("    - Error ruins (CanCollect throws): " + errorRuins);
                console.log("    - Healthy ruins (Health>0): " + healthyRuins);
                console.log("    - Zero health ruins (Health=0): " + zeroHealthRuins);
                console.log("    - In cooldown ruins: " + inCooldownRuins);

                console.log("[+] Sample instances:");
                examples.forEach(function(ex, i) {
                    console.log("  " + (i+1) + ". " + ex.ptr + " - Health: " + ex.health + ", CanCollect: " + ex.canCollect + ", Cooldown: " + ex.cooldown + "s, Status: " + ex.status);
                });

                console.log("[+] Analysis:");
                if (collectibleRuins === 0 && (collectedRuins > 0 || errorRuins > 0)) {
                    console.log("    → All ruins appear to be already collected");
                    console.log("    → Use scanForSellableRuins() to find sellable ones");
                } else if (collectibleRuins > 0) {
                    console.log("    → " + collectibleRuins + " ruins are still collectible");
                    console.log("    → Use collectDiscoveredRuins() to collect them");
                } else {
                    console.log("    → No GoodyHutHelper instances found");
                }

                console.log("==================================");

            }, 3000); // Wait 3 seconds for scanning

            return {
                scanned: scannedCount,
                collectible: collectibleRuins,
                collected: collectedRuins + errorRuins,
                healthy: healthyRuins
            };
        },
        scanForSellableRuins: function() {
            console.log("[+] ===== SCANNING FOR SELLABLE RUINS =====");
            console.log("[*] Scanning for collected but unsold ruins...");

            var scannedCount = 0;
            var sellableRuins = [];
            var nonSellableRuins = [];

            // Scan for sellable ruins by hooking Update() temporarily
            var scanHook = Interceptor.attach(updateAddr, {
                onEnter: function(args) {
                    scannedCount++;

                    // Limit scanning to prevent infinite loop
                    if (scannedCount > 500) {
                        return;
                    }

                    var thisPtr = this.context.x0;
                    var instanceId = thisPtr.toString();

                    try {
                        // First, try to get basic info about the ruin
                        var getHealthFunc = new NativeFunction(il2cpp.base.add(0x209EA1C), 'int', ['pointer']); // GetHealth() RVA
                        var health = getHealthFunc(thisPtr);

                        // If health > 0, this might be a ruin we can analyze
                        if (health > 0) {
                            var canCollect = null;
                            var canCollectError = false;

                            // Try to call CanCollect() - it may return null/throw for collected ruins
                            try {
                                var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                                canCollect = canCollectFunc(thisPtr);
                            } catch (e) {
                                canCollectError = true;
                                // CanCollect() threw an error - likely a collected ruin
                            }

                            // Get additional info about the ruin
                            var getCooldownFunc = new NativeFunction(il2cpp.base.add(0x209E9B0), 'double', ['pointer']); // GetCooldownTimeLeft() RVA
                            var cooldownTime = getCooldownFunc(thisPtr);

                            var getRewardTypeFunc = new NativeFunction(il2cpp.base.add(0x209CC3C), 'int', ['pointer']); // GetRewardType() RVA
                            var rewardType = getRewardTypeFunc(thisPtr);

                            var getRewardAmountFunc = new NativeFunction(il2cpp.base.add(0x209D9C4), 'int', ['pointer']); // GetRewardAmount() RVA
                            var rewardAmount = getRewardAmountFunc(thisPtr);

                            var ruinInfo = {
                                ptr: thisPtr.toString(),
                                instanceId: instanceId,
                                health: health,
                                cooldownTime: cooldownTime.toFixed(1),
                                rewardType: rewardType,
                                rewardAmount: rewardAmount,
                                canCollect: canCollect,
                                canCollectError: canCollectError
                            };

                            // Determine ruin status
                            if (canCollect === 1) {
                                // Still collectible - not what we're looking for
                                console.log("[*] COLLECTIBLE: " + thisPtr + " (Health: " + health + ", Reward: " + rewardAmount + " type " + rewardType + ")");
                            } else if (canCollect === 0 || canCollectError) {
                                // Either CanCollect returned 0 or threw error - likely collected
                                if (cooldownTime <= 0) {
                                    sellableRuins.push(ruinInfo);
                                    console.log("[+] SELLABLE: " + thisPtr + " (Health: " + health + ", Reward: " + rewardAmount + " type " + rewardType + ", CanCollect: " + (canCollectError ? "ERROR" : canCollect) + ")");
                                } else {
                                    ruinInfo.reason = "In cooldown (" + cooldownTime.toFixed(1) + "s)";
                                    nonSellableRuins.push(ruinInfo);
                                    console.log("[*] NON-SELLABLE: " + thisPtr + " - " + ruinInfo.reason + " (CanCollect: " + (canCollectError ? "ERROR" : canCollect) + ")");
                                }
                            }
                        }
                    } catch (e) {
                        // Silently ignore errors during scanning
                    }
                }
            });

            // Wait for scanning to complete
            setTimeout(function() {
                scanHook.detach();

                console.log("[+] ===== SCAN COMPLETE =====");
                console.log("[+] Scan Results:");
                console.log("    - Total instances scanned: " + scannedCount);
                console.log("    - Sellable ruins found: " + sellableRuins.length);
                console.log("    - Non-sellable ruins found: " + nonSellableRuins.length);

                if (sellableRuins.length > 0) {
                    console.log("[+] Sellable Ruins Details:");
                    sellableRuins.forEach(function(ruin, index) {
                        console.log("  " + (index + 1) + ". " + ruin.ptr + " - Health: " + ruin.health + ", Reward: " + ruin.rewardAmount + " (type " + ruin.rewardType + ")");
                    });
                    console.log("[*] Use rpc.exports.sellCollectedRuins() to sell these ruins");
                } else {
                    console.log("[*] No sellable ruins found");
                }

                if (nonSellableRuins.length > 0) {
                    console.log("[*] Non-sellable Ruins Summary:");
                    var cooldownCount = nonSellableRuins.filter(r => r.reason && r.reason.includes("cooldown")).length;
                    var noHealthCount = nonSellableRuins.filter(r => r.reason && r.reason.includes("No health")).length;
                    console.log("    - In cooldown: " + cooldownCount);
                    console.log("    - No health: " + noHealthCount);
                }

                console.log("=============================");

            }, 2000); // Wait 2 seconds for scanning to complete

            return {
                sellable: sellableRuins.length,
                nonSellable: nonSellableRuins.length,
                scanned: scannedCount
            };
        },
        sellCollectedRuins: function() {
            console.log("[+] ===== REAL-TIME SELLING STARTED =====");
            console.log("[*] Scanning and selling collected ruins immediately...");

            var scannedCount = 0;
            var soldCount = 0;
            var failedCount = 0;
            var skippedCount = 0;

            // Real-time selling: scan and sell immediately
            var realtimeSellHook = Interceptor.attach(updateAddr, {
                onEnter: function(args) {
                    scannedCount++;

                    // Limit scanning to prevent infinite loop
                    if (scannedCount > 1000) {
                        return;
                    }

                    var thisPtr = this.context.x0;
                    var instanceId = thisPtr.toString();

                    try {
                        // Check health first - only process ruins that exist
                        var getHealthFunc = new NativeFunction(il2cpp.base.add(0x209EA1C), 'int', ['pointer']);
                        var health = getHealthFunc(thisPtr);

                        if (health > 0) {
                            var canCollect = null;
                            var canCollectError = false;

                            // Try CanCollect() to determine if collected
                            try {
                                var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                                canCollect = canCollectFunc(thisPtr);
                            } catch (e) {
                                canCollectError = true;
                            }

                            // If CanCollect=0 or throws error, it's collected
                            if (canCollect === 0 || canCollectError) {
                                // Check cooldown before selling
                                var getCooldownFunc = new NativeFunction(il2cpp.base.add(0x209E9B0), 'double', ['pointer']);
                                var cooldownTime = getCooldownFunc(thisPtr);

                                if (cooldownTime > 0) {
                                    skippedCount++;
                                    if (scannedCount <= 10) {
                                        console.log("[*] Skipping ruin in cooldown: " + thisPtr + " (" + cooldownTime.toFixed(1) + "s)");
                                    }
                                    return;
                                }

                                // SELL IMMEDIATELY while pointer is fresh
                                try {
                                    console.log("[*] SELLING: " + thisPtr + " (Health: " + health + ", CanCollect: " + (canCollectError ? "ERROR" : canCollect) + ")");

                                    // Validate pointer one more time before selling
                                    if (thisPtr.isNull() || thisPtr.equals(ptr(0))) {
                                        console.log("[-] Invalid pointer detected, skipping");
                                        failedCount++;
                                        return;
                                    }

                                    // Call SellRuins() immediately on fresh pointer
                                    var sellRuinsAddr = il2cpp.base.add(0x209DE3C);  // SellRuins() RVA
                                    var sellRuinsFunc = new NativeFunction(sellRuinsAddr, 'void', ['pointer']);
                                    sellRuinsFunc(thisPtr);

                                    soldCount++;
                                    console.log("[+] SOLD: " + thisPtr + " (Total sold: " + soldCount + ")");

                                } catch (e) {
                                    failedCount++;
                                    console.log("[-] SELL FAILED: " + thisPtr + " - " + e);
                                }
                            }
                        }
                    } catch (e) {
                        // Silently ignore scanning errors
                    }
                }
            });

            // Stop scanning after 5 seconds
            setTimeout(function() {
                realtimeSellHook.detach();

                console.log("[+] ===== REAL-TIME SELLING COMPLETE =====");
                console.log("[+] Results:");
                console.log("    - Instances scanned: " + scannedCount);
                console.log("    - Successfully sold: " + soldCount);
                console.log("    - Failed to sell: " + failedCount);
                console.log("    - Skipped (cooldown): " + skippedCount);
                console.log("    - Success rate: " + (soldCount > 0 ? ((soldCount / (soldCount + failedCount)) * 100).toFixed(1) + "%" : "0%"));
                console.log("==========================================");

            }, 5000); // 5 second scanning window

            return {
                scanned: scannedCount,
                sold: soldCount,
                failed: failedCount,
                skipped: skippedCount
            };
        },
        collectDiscoveredRuins: function() {
            console.log("[+] ===== MANUAL COLLECTION STARTED =====");
            console.log("[*] Collecting " + discoveredInstances.size + " discovered ruins...");

            if (discoveredInstances.size === 0) {
                console.log("[*] No discovered ruins to collect");
                return {collected: 0, failed: 0};
            }

            var collectedCount = 0;
            var failedCount = 0;
            var ruinsToCollect = Array.from(discoveredInstances.entries());

            ruinsToCollect.forEach(function(entry, index) {
                var instanceId = entry[0];
                var ruinData = entry[1];
                var thisPtr = ruinData.ptr;

                setTimeout(function() {
                    try {
                        console.log("[*] Collecting ruin " + (index + 1) + "/" + ruinsToCollect.length + " at: " + thisPtr);

                        // Validate pointer is still valid
                        if (thisPtr.isNull() || thisPtr.equals(ptr(0))) {
                            console.log("[-] Invalid pointer for ruin " + instanceId);
                            failedCount++;
                            return;
                        }

                        // Verify still collectible
                        var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                        var canCollect = canCollectFunc(thisPtr);

                        if (canCollect !== 1) {
                            console.log("[*] Ruin " + instanceId + " no longer collectible, skipping");
                            return;
                        }

                        console.log("[*] Step 1: Attempting to get Config() for: " + instanceId);

                        // Get config and set cleanUp flag with detailed error tracking
                        var configAddr = il2cpp.base.add(0x209B54C);  // Config() RVA
                        var configFunc = new NativeFunction(configAddr, 'pointer', ['pointer']);
                        var configPtr = null;

                        try {
                            configPtr = configFunc(thisPtr);
                            console.log("[*] Step 2: Config() returned: " + configPtr + " for: " + instanceId);
                        } catch (e) {
                            console.log("[-] Step 2 FAILED: Config() error for " + instanceId + ": " + e);
                            throw e; // Re-throw to stop processing this ruin
                        }

                        if (configPtr && !configPtr.isNull() && !configPtr.equals(ptr(0))) {
                            try {
                                console.log("[*] Step 3: Setting cleanUp flag for: " + instanceId);
                                var cleanUpOffset = 0x30;
                                configPtr.add(cleanUpOffset).writeU8(1);
                                console.log("[+] Step 3 SUCCESS: Set cleanUp flag for: " + instanceId);
                            } catch (e) {
                                console.log("[-] Step 3 FAILED: Error setting cleanUp flag for " + instanceId + ": " + e);
                                // Continue anyway - cleanUp flag is optional
                            }
                        } else {
                            console.log("[-] Step 3 SKIPPED: Config() returned null/invalid pointer for " + instanceId);
                        }

                        console.log("[*] Step 4: Attempting FinishCollect() for: " + instanceId);

                        // Execute collection with detailed error tracking
                        var finishCollectFunc = new NativeFunction(finishCollectAddr, 'void', ['pointer']);
                        try {
                            finishCollectFunc(thisPtr);
                            console.log("[+] Step 4 SUCCESS: FinishCollect() completed for: " + instanceId);
                        } catch (e) {
                            console.log("[-] Step 4 FAILED: FinishCollect() error for " + instanceId + ": " + e);
                            throw e; // Re-throw to mark this as failed
                        }

                        // Mark as processed and remove from discovered
                        processedInstances.add(instanceId);
                        discoveredInstances.delete(instanceId);
                        collectedCount++;
                        console.log("[+] Successfully collected ruin at: " + thisPtr + " (" + collectedCount + " collected)");

                    } catch (e) {
                        console.log("[-] Error collecting ruin " + instanceId + ": " + e);
                        failedCount++;

                        // Add to failed instances
                        failedInstances.set(instanceId, {
                            ptr: thisPtr,
                            lastFailTime: Date.now(),
                            retryCount: 1
                        });
                    }

                    // Log final results after last ruin
                    if (index === ruinsToCollect.length - 1) {
                        setTimeout(function() {
                            console.log("[+] ===== MANUAL COLLECTION COMPLETE =====");
                            console.log("[+] Results:");
                            console.log("    - Successfully collected: " + collectedCount);
                            console.log("    - Failed to collect: " + failedCount);
                            console.log("    - Total processed: " + processedInstances.size);
                            console.log("    - Remaining discovered: " + discoveredInstances.size);
                            console.log("==========================================");
                        }, 500);
                    }
                }, index * 200); // 200ms delay between each collection
            });

            return {collected: collectedCount, failed: failedCount};
        },
        collectDiscoveredRuinsSimple: function() {
            console.log("[+] ===== SIMPLE COLLECTION (NO CONFIG) =====");
            console.log("[*] Collecting discovered ruins WITHOUT Config() calls...");

            if (discoveredInstances.size === 0) {
                console.log("[*] No discovered ruins to collect");
                return {collected: 0, failed: 0};
            }

            var collectedCount = 0;
            var failedCount = 0;
            var ruinsToCollect = Array.from(discoveredInstances.entries());

            ruinsToCollect.forEach(function(entry, index) {
                var instanceId = entry[0];
                var ruinData = entry[1];
                var thisPtr = ruinData.ptr;

                setTimeout(function() {
                    try {
                        console.log("[*] Simple collecting ruin " + (index + 1) + "/" + ruinsToCollect.length + " at: " + thisPtr);

                        // Validate pointer
                        if (thisPtr.isNull() || thisPtr.equals(ptr(0))) {
                            console.log("[-] Invalid pointer for ruin " + instanceId);
                            failedCount++;
                            return;
                        }

                        // Verify still collectible
                        var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                        var canCollect = canCollectFunc(thisPtr);

                        if (canCollect !== 1) {
                            console.log("[*] Ruin " + instanceId + " no longer collectible, skipping");
                            return;
                        }

                        console.log("[*] Calling FinishCollect() directly (no Config() call)");

                        // Execute collection WITHOUT Config() call
                        var finishCollectFunc = new NativeFunction(finishCollectAddr, 'void', ['pointer']);
                        finishCollectFunc(thisPtr);

                        // Mark as processed and remove from discovered
                        processedInstances.add(instanceId);
                        discoveredInstances.delete(instanceId);
                        collectedCount++;
                        console.log("[+] Simple collection SUCCESS: " + thisPtr + " (" + collectedCount + " collected)");

                    } catch (e) {
                        console.log("[-] Simple collection FAILED for " + instanceId + ": " + e);
                        failedCount++;
                    }

                    // Log final results
                    if (index === ruinsToCollect.length - 1) {
                        setTimeout(function() {
                            console.log("[+] ===== SIMPLE COLLECTION COMPLETE =====");
                            console.log("[+] Results:");
                            console.log("    - Successfully collected: " + collectedCount);
                            console.log("    - Failed to collect: " + failedCount);
                            console.log("    - Success rate: " + (collectedCount > 0 ? ((collectedCount / (collectedCount + failedCount)) * 100).toFixed(1) + "%" : "0%"));
                            console.log("==========================================");
                        }, 500);
                    }
                }, index * 200);
            });

            return {collected: collectedCount, failed: failedCount};
        },
        getStats: function() {
            return {
                phase: currentPhase,
                discovered: discoveredInstances.size,
                processed: processedInstances.size,
                failed: failedInstances.size,
                batchNumber: batchNumber,
                isProcessing: isProcessingBatch,
                totalScans: totalDiscoveryScans,
                lastDiscovery: lastDiscoveryTime,
                discoveryComplete: discoveryCompleteTime
            };
        }
    };

    // Hook GetRewardType and GetRewardAmount for logging
    Interceptor.attach(il2cpp.base.add(0x209CC3C), {  // GetRewardType RVA
        onLeave: function(retval) {
            var rewardType = retval.toInt32();
            console.log("[*] Reward Type: " + rewardType);
        }
    });
    
    Interceptor.attach(il2cpp.base.add(0x209D9C4), {  // GetRewardAmount RVA
        onLeave: function(retval) {
            var amount = retval.toInt32();
            console.log("[*] Reward Amount: " + amount);
        }
    });
    
        // Sell Collected Ruins Function
        function sellCollectedRuins(autoMode = false) {
            console.log("[+] ===== SELLING COLLECTED RUINS =====");
            console.log("[*] Scanning for collected but unsold ruins...");

            var soldCount = 0;
            var failedCount = 0;
            var scannedCount = 0;
            var collectedRuins = [];

            // Scan for collected ruins by hooking Update() temporarily
            var sellScanHook = Interceptor.attach(updateAddr, {
                onEnter: function(args) {
                    scannedCount++;

                    // Limit scanning to prevent infinite loop
                    if (scannedCount > 500) {
                        return;
                    }

                    var thisPtr = this.context.x0;
                    var instanceId = thisPtr.toString();

                    try {
                        // First check health to see if ruin exists
                        var getHealthFunc = new NativeFunction(il2cpp.base.add(0x209EA1C), 'int', ['pointer']); // GetHealth() RVA
                        var health = getHealthFunc(thisPtr);

                        // Only process ruins with health > 0
                        if (health > 0) {
                            var canCollect = null;
                            var canCollectError = false;

                            // Try CanCollect() - may throw error for collected ruins
                            try {
                                var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                                canCollect = canCollectFunc(thisPtr);
                            } catch (e) {
                                canCollectError = true;
                                // CanCollect() threw error - likely collected ruin
                            }

                            // If CanCollect returns 0 OR throws error, it's likely collected
                            if (canCollect === 0 || canCollectError) {
                                collectedRuins.push({
                                    ptr: thisPtr,
                                    id: instanceId,
                                    health: health,
                                    canCollectError: canCollectError
                                });
                                console.log("[*] Found collected ruin at: " + thisPtr + " (health: " + health + ", CanCollect: " + (canCollectError ? "ERROR" : canCollect) + ")");
                            }
                        }
                    } catch (e) {
                        // Silently ignore errors during scanning
                    }
                }
            });

            // Wait for scanning to complete
            setTimeout(function() {
                sellScanHook.detach();

                console.log("[*] Scan complete. Found " + collectedRuins.length + " potential collected ruins");

                if (collectedRuins.length === 0) {
                    console.log("[*] No collected ruins found to sell");
                    return {sold: 0, failed: 0, scanned: scannedCount};
                }

                // Process each collected ruin
                collectedRuins.forEach(function(ruin, index) {
                    setTimeout(function() {
                        try {
                            console.log("[*] Attempting to sell ruin " + (index + 1) + "/" + collectedRuins.length + " at: " + ruin.ptr);

                            // Validate pointer is still valid
                            if (ruin.ptr.isNull() || ruin.ptr.equals(ptr(0))) {
                                console.log("[-] Invalid pointer for ruin " + ruin.id);
                                failedCount++;
                                return;
                            }

                            // Double-check the ruin is still in a sellable state
                            var canCollectFunc = new NativeFunction(canCollectAddr, 'int', ['pointer']);
                            var canCollect = canCollectFunc(ruin.ptr);

                            if (canCollect === 1) {
                                console.log("[*] Ruin " + ruin.id + " is now collectible again, skipping sale");
                                return;
                            }

                            // Check if ruin is in cooldown (might not be sellable)
                            var getCooldownFunc = new NativeFunction(il2cpp.base.add(0x209E9B0), 'double', ['pointer']); // GetCooldownTimeLeft() RVA
                            var cooldownTime = getCooldownFunc(ruin.ptr);

                            if (cooldownTime > 0) {
                                console.log("[*] Ruin " + ruin.id + " is in cooldown (" + cooldownTime.toFixed(1) + "s), skipping");
                                return;
                            }

                            // Execute SellRuins()
                            var sellRuinsAddr = il2cpp.base.add(0x209DE3C);  // SellRuins() RVA
                            var sellRuinsFunc = new NativeFunction(sellRuinsAddr, 'void', ['pointer']);

                            sellRuinsFunc(ruin.ptr);
                            soldCount++;
                            console.log("[+] Successfully sold ruin at: " + ruin.ptr + " (" + soldCount + " sold)");

                        } catch (e) {
                            console.log("[-] Error selling ruin " + ruin.id + ": " + e);
                            failedCount++;
                        }

                        // Log final results after last ruin
                        if (index === collectedRuins.length - 1) {
                            setTimeout(function() {
                                console.log("[+] ===== SELLING COMPLETE =====");
                                console.log("[+] Results:");
                                console.log("    - Ruins scanned: " + scannedCount);
                                console.log("    - Potential collected ruins found: " + collectedRuins.length);
                                console.log("    - Successfully sold: " + soldCount);
                                console.log("    - Failed to sell: " + failedCount);
                                console.log("===============================");
                            }, 500);
                        }
                    }, index * 300); // 300ms delay between each sale attempt
                });

            }, 2000); // Wait 2 seconds for scanning to complete

            return {sold: soldCount, failed: failedCount, scanned: scannedCount};
        }

        console.log("[+] Ruins Auto-Collector hooks installed");
        console.log("[+] Will auto-collect any ruins when CanCollect() returns true");
        console.log("[+] SellCollectedRuins function available");
    } // End of initializeHooks function
});

// Helper function to manually trigger collection on specific entity
function manualCollect(goodyHutAddress) {
    var goodyHutPtr = ptr(goodyHutAddress);

    // Use the same enhanced module detection logic
    var il2cpp = null;
    var potentialModules = [];

    Process.enumerateModules().forEach(function(module) {
        var moduleName = module.name.toLowerCase();

        // Check for obvious patterns first
        if (moduleName.includes("il2cpp") ||
            moduleName.includes("unity") ||
            moduleName.includes("libmain") ||
            moduleName.includes("domination") ||
            moduleName.includes("nexon")) {
            il2cpp = module;
            return;
        }

        // Collect potential candidates
        if (module.size > 5000000) { // > 5MB
            potentialModules.push(module);
        }
    });

    // If no obvious module found, try the largest potential candidate
    if (!il2cpp && potentialModules.length > 0) {
        potentialModules.sort(function(a, b) { return b.size - a.size; });
        il2cpp = potentialModules[0];
        console.log("[*] Using largest module as IL2CPP candidate: " + il2cpp.name);
    }

    if (!il2cpp) {
        console.log("[-] IL2CPP module not found for manual collection");
        return false;
    }

    try {
        // Validate pointer first
        if (goodyHutPtr.isNull() || goodyHutPtr.equals(ptr(0))) {
            console.log("[-] Invalid GoodyHutHelper pointer: " + goodyHutAddress);
            return false;
        }

        var canCollectFunc = new NativeFunction(il2cpp.base.add(0x209D258), 'int', ['pointer']);
        var finishCollectFunc = new NativeFunction(il2cpp.base.add(0x209B924), 'void', ['pointer']);
        var configFunc = new NativeFunction(il2cpp.base.add(0x209B54C), 'pointer', ['pointer']);

        // Check if collectible with error handling
        var canCollect;
        try {
            canCollect = canCollectFunc(goodyHutPtr);
        } catch (e) {
            console.log("[-] Error calling CanCollect(): " + e);
            return false;
        }

        if (canCollect === 1) {
            // Safely set cleanUp flag
            var configPtr = null;
            try {
                configPtr = configFunc(goodyHutPtr);
                console.log("[*] Manual collection - Config() returned: " + configPtr);
            } catch (e) {
                console.log("[-] Error calling Config(): " + e);
                // Continue without setting cleanUp flag
            }

            if (configPtr && !configPtr.isNull() && !configPtr.equals(ptr(0))) {
                try {
                    // Validate memory access before writing
                    var cleanUpAddr = configPtr.add(0x30);
                    var currentValue = cleanUpAddr.readU8();
                    console.log("[*] Manual collection - Current cleanUp value: " + currentValue);

                    cleanUpAddr.writeU8(1);
                    console.log("[+] Manual collection - Set cleanUp flag");
                } catch (e) {
                    console.log("[-] Error setting cleanUp flag: " + e);
                    // Continue with collection anyway
                }
            } else {
                console.log("[-] Manual collection - Config() returned null/invalid pointer");
            }

            // Execute collection
            try {
                finishCollectFunc(goodyHutPtr);
                console.log("[+] Manual collection completed for: " + goodyHutAddress);
                return true;
            } catch (e) {
                console.log("[-] Error calling FinishCollect(): " + e);
                return false;
            }
        } else {
            console.log("[-] Cannot collect ruins at: " + goodyHutAddress + " (CanCollect returned: " + canCollect + ")");
            return false;
        }
    } catch (e) {
        console.log("[-] Error in manual collection: " + e);
        return false;
    }
}

// Usage: manualCollect("0x12345678")  // Replace with actual GoodyHutHelper instance address